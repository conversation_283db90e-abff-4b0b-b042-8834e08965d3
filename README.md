# Qt5 音乐播放器

基于Qt5开发的音乐播放器，使用QWidget作为基类，具有完整的音乐播放功能。

## 功能特性

### 用户认证
- 精美的账号密码登录界面
- 登录成功动画效果（进度条显示）
- 登录成功后自动跳转到音乐播放界面
- 默认账号：
  - 用户名: admin, 密码: 123456
  - 用户名: user, 密码: password

### 音乐播放功能
- 播放/暂停控制
- 上一曲/下一曲切换
- 停止播放
- 播放进度显示和控制

### 音量控制
- 音量滑块控制
- 音量数值显示
- 音量加减按钮

### 播放模式
- 顺序播放
- 列表循环
- 单曲循环
- 随机播放

### 歌单管理
- 播放列表滚动显示（最多显示5个项目）
- 添加音乐文件（正确显示歌名）
- 删除音乐文件
- 双击播放选中歌曲
- ❤ 我喜欢的功能（收藏/取消收藏）

### 主题系统
- 🎨 主题切换功能
- 📷 添加自定义主题图片
- 切换歌曲自动切换主题
- 主题透明化效果（75%透明度）
- 多种内置渐变主题（蓝色、紫色、绿色、橙色）

## 技术特点

- 基于Qt5框架开发
- 使用QWidget作为基类
- QMediaPlayer提供音频播放功能
- 支持多种音频格式（MP3, WAV, FLAC, AAC, OGG, WMA）
- 现代化的用户界面设计
- 响应式布局

## 编译要求

- Qt 5.x
- Qt Multimedia模块
- C++11支持

## 编译方法

```bash
qmake music11.pro
make
```

或者在Qt Creator中直接打开music11.pro文件进行编译。

## 使用说明

1. 启动程序后会显示精美的登录界面
2. 输入用户名和密码进行登录（admin/123456）
3. 登录成功后显示进度条动画，自动跳转到音乐播放器
4. 点击"添加音乐"按钮选择音乐文件（自动显示正确歌名）
5. 双击播放列表中的歌曲开始播放（列表最多显示5个，可滚动）
6. 使用播放控制按钮控制播放状态
7. 使用音量控制调节音量（进度条+数值显示）
8. 点击播放模式按钮切换播放模式
9. 点击"❤ 我喜欢的"收藏/取消收藏当前歌曲
10. 点击"🎨 主题切换"手动切换主题
11. 点击"📷 添加主题"添加自定义主题图片
12. 切换歌曲时会自动切换主题背景

## 项目结构

```
├── main.cpp                 # 程序入口
├── loginwidget.h/cpp        # 登录界面
├── musicplayerwidget.h/cpp  # 音乐播放器主界面
├── *.ui                     # UI界面文件
├── resources.qrc            # 资源文件
├── music11.pro              # 项目配置文件
└── README.md                # 说明文档
```

## 开发者

基于Qt5 QWidget开发的音乐播放器应用程序。
