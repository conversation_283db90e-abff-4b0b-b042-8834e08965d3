@echo off
echo ========================================
echo Qt5音乐播放器编译脚本
echo ========================================
echo.

REM 检查Qt环境
echo 正在检查Qt环境...
qmake -v >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到qmake命令！
    echo 请确保Qt开发环境已正确安装并添加到PATH中
    echo.
    echo 常见Qt安装路径：
    echo   C:\Qt\5.15.2\msvc2019_64\bin
    echo   C:\Qt\6.x.x\msvc2019_64\bin
    echo.
    pause
    exit /b 1
)

echo Qt环境检查通过！
echo.

REM 清理之前的编译文件
echo 正在清理旧文件...
if exist Makefile del /q Makefile
if exist Makefile.Debug del /q Makefile.Debug
if exist Makefile.Release del /q Makefile.Release
if exist *.obj del /q *.obj
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release
if exist *.exe del /q *.exe

echo 清理完成！
echo.

REM 生成Makefile
echo 正在生成Makefile...
qmake music11.pro
if errorlevel 1 (
    echo 错误：Makefile生成失败！
    echo 请检查music11.pro文件是否正确
    pause
    exit /b 1
)

echo Makefile生成成功！
echo.

REM 编译项目
echo 正在编译项目...
if exist Makefile (
    nmake
    if errorlevel 1 (
        echo.
        echo 编译失败！请检查上面的错误信息
        echo.
        echo 常见问题解决方案：
        echo 1. 确保已安装Visual Studio或Build Tools
        echo 2. 确保Qt Multimedia模块已安装
        echo 3. 检查代码语法错误
        pause
        exit /b 1
    )

    REM 检查生成的可执行文件
    if exist debug\MusicPlayer.exe (
        echo.
        echo ========================================
        echo 编译成功！
        echo 可执行文件：debug\MusicPlayer.exe
        echo ========================================
        echo.
        echo 是否立即运行程序？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            start debug\MusicPlayer.exe
        )
    ) else if exist release\MusicPlayer.exe (
        echo.
        echo ========================================
        echo 编译成功！
        echo 可执行文件：release\MusicPlayer.exe
        echo ========================================
        echo.
        echo 是否立即运行程序？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            start release\MusicPlayer.exe
        )
    ) else (
        echo.
        echo 警告：编译完成但未找到可执行文件
        echo 请检查编译输出信息
    )
) else (
    echo 错误：未找到Makefile文件
)

echo.
pause
