@echo off
echo 正在编译Qt5音乐播放器...

REM 设置Qt环境变量（请根据实际Qt安装路径修改）
REM set QTDIR=C:\Qt\5.15.2\msvc2019_64
REM set PATH=%QTDIR%\bin;%PATH%

REM 清理之前的编译文件
if exist Makefile del Makefile
if exist *.obj del *.obj
if exist *.exe del *.exe

REM 生成Makefile
qmake music11.pro

REM 编译项目
if exist Makefile (
    echo Makefile生成成功，开始编译...
    nmake
    if exist MusicPlayer.exe (
        echo 编译成功！可执行文件：MusicPlayer.exe
    ) else (
        echo 编译失败，请检查错误信息
    )
) else (
    echo Makefile生成失败，请检查qmake是否正确安装
)

pause
