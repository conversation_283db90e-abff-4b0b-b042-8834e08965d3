#include "loginwidget.h"
#include "musicplayerwidget.h"
#include <QApplication>

LoginWidget::LoginWidget(QWidget *parent)
    : QWidget(parent)
    , musicPlayer(nullptr)
    , successTimer(nullptr)
{
    setupUI();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("🎵 音乐播放器 - 登录");
    setFixedSize(480, 600);  // 增加高度以适应现代化设计
    
    // 设置现代化样式
    setStyleSheet(getModernLoginStyle());
}

LoginWidget::~LoginWidget()
{
}

void LoginWidget::setupUI()
{
    // 创建现代化登录卡片
    loginCard = new QWidget(this);
    loginCard->setObjectName("loginCard");

    // 创建UI组件
    titleLabel = new QLabel("🎵 音乐播放器", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);

    // 移除传统标签，使用浮动标签设计
    usernameLabel = new QLabel("", this);  // 隐藏传统标签
    passwordLabel = new QLabel("", this);  // 隐藏传统标签
    usernameLabel->hide();
    passwordLabel->hide();

    // 现代化输入框设计
    usernameEdit = new QLineEdit(this);
    usernameEdit->setObjectName("modernInput");
    usernameEdit->setPlaceholderText("用户名");

    passwordEdit = new QLineEdit(this);
    passwordEdit->setObjectName("modernInput");
    passwordEdit->setPlaceholderText("密码");
    passwordEdit->setEchoMode(QLineEdit::Password);

    // 现代化按钮设计
    loginButton = new QPushButton("登录", this);
    loginButton->setObjectName("primaryButton");

    registerButton = new QPushButton("注册", this);
    registerButton->setObjectName("secondaryButton");

    // 添加生物识别图标（视觉展示）
    biometricIcon = new QLabel("👆", this);
    biometricIcon->setObjectName("biometricIcon");
    biometricIcon->setAlignment(Qt::AlignCenter);
    biometricIcon->setToolTip("生物识别登录（演示）");

    // 创建登录成功提示组件
    successLabel = new QLabel("登录成功！正在进入音乐播放器...", this);
    successLabel->setObjectName("successLabel");
    successLabel->setAlignment(Qt::AlignCenter);
    successLabel->hide();

    loadingBar = new QProgressBar(this);
    loadingBar->setRange(0, 100);
    loadingBar->setValue(0);
    loadingBar->hide();

    // 创建透明度效果和动画
    opacityEffect = new QGraphicsOpacityEffect(this);
    fadeAnimation = new QPropertyAnimation(opacityEffect, "opacity", this);

    // 创建现代化布局
    mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(40, 40, 40, 40);
    mainLayout->setSpacing(0);

    // 卡片内部布局
    QVBoxLayout *cardLayout = new QVBoxLayout(loginCard);
    cardLayout->setContentsMargins(32, 40, 32, 40);
    cardLayout->setSpacing(24);

    // 设置现代化布局
    mainLayout->addStretch(1);

    // 标题区域
    cardLayout->addWidget(titleLabel);
    cardLayout->addSpacing(16);

    // 输入框区域
    cardLayout->addWidget(usernameEdit);
    cardLayout->addSpacing(16);
    cardLayout->addWidget(passwordEdit);
    cardLayout->addSpacing(24);

    // 按钮区域
    buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(16);
    buttonLayout->addWidget(registerButton);
    buttonLayout->addWidget(loginButton);
    cardLayout->addLayout(buttonLayout);

    // 生物识别区域
    cardLayout->addSpacing(20);
    cardLayout->addWidget(biometricIcon);

    // 添加卡片到主布局
    mainLayout->addWidget(loginCard);
    mainLayout->addStretch(1);

    // 添加登录成功提示组件（在卡片外）
    mainLayout->addWidget(successLabel);
    mainLayout->addWidget(loadingBar);

    setLayout(mainLayout);
}

void LoginWidget::setupConnections()
{
    connect(loginButton, &QPushButton::clicked, this, &LoginWidget::onLoginClicked);
    connect(registerButton, &QPushButton::clicked, this, &LoginWidget::onRegisterClicked);

    // 回车键登录
    connect(passwordEdit, &QLineEdit::returnPressed, this, &LoginWidget::onLoginClicked);

    // 登录成功动画连接
    connect(fadeAnimation, &QPropertyAnimation::finished, this, &LoginWidget::hideLoginSuccess);
}

void LoginWidget::onLoginClicked()
{
    QString username = usernameEdit->text().trimmed();
    QString password = passwordEdit->text().trimmed();
    
    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "警告", "用户名和密码不能为空！");
        return;
    }
    
    if (validateLogin(username, password)) {
        // 显示登录成功动画
        showLoginSuccess();
    } else {
        QMessageBox::warning(this, "错误", "用户名或密码错误！");
        passwordEdit->clear();
        passwordEdit->setFocus();
    }
}

void LoginWidget::onRegisterClicked()
{
    QMessageBox::information(this, "注册", "注册功能暂未实现，请使用默认账号：\n用户名: admin\n密码: 123456");
}

bool LoginWidget::validateLogin(const QString &username, const QString &password)
{
    // 简单的验证逻辑，实际项目中应该连接数据库
    return (username == "admin" && password == "123456") ||
           (username == "user" && password == "password");
}

void LoginWidget::showLoginSuccess()
{
    // 禁用登录按钮
    loginButton->setEnabled(false);
    registerButton->setEnabled(false);

    // 显示成功提示和进度条
    successLabel->show();
    loadingBar->show();

    // 启动进度条动画
    loadingBar->setValue(0);

    // 创建定时器模拟加载过程
    successTimer = new QTimer(this);
    connect(successTimer, &QTimer::timeout, [this]() {
        int value = loadingBar->value();
        if (value < 100) {
            loadingBar->setValue(value + 10);
        } else {
            successTimer->stop();
            // 加载完成，跳转到音乐播放器
            if (!musicPlayer) {
                musicPlayer = new MusicPlayerWidget();
            }
            musicPlayer->show();
            this->hide();
        }
    });
    successTimer->start(100); // 每100ms更新一次
}

void LoginWidget::hideLoginSuccess()
{
    successLabel->hide();
    loadingBar->hide();
    loginButton->setEnabled(true);
    registerButton->setEnabled(true);
}

// 现代化登录界面样式
QString LoginWidget::getModernLoginStyle()
{
    return QString(
        /* 现代化背景 - 毛玻璃渐变效果 */
        "LoginWidget {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 rgba(103, 80, 164, 0.9), "      /* MD3 Primary */
        "                stop:0.3 rgba(135, 206, 250, 0.8), "   /* 天空蓝 */
        "                stop:0.7 rgba(255, 182, 193, 0.8), "   /* 浅粉色 */
        "                stop:1 rgba(152, 251, 152, 0.9));"     /* 浅绿色 */
        "}"

        /* 现代化登录卡片 */
        "QWidget#loginCard {"
        "    background: rgba(255, 255, 255, 0.95);"           /* 毛玻璃白色背景 */
        "    border-radius: 24px;"                             /* 大圆角 */
        "    border: 1px solid rgba(255, 255, 255, 0.3);"
        "    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);"      /* 深阴影 */
        "    backdrop-filter: blur(20px);"                     /* 毛玻璃模糊 */
        "}"

        /* 现代化标题 */
        "QLabel#titleLabel {"
        "    color: #1D1B20;"                                  /* MD3 On Surface */
        "    font-size: 32px;"
        "    font-weight: 600;"                                /* Semibold */
        "    margin: 0px;"
        "    padding: 8px 0px;"
        "}"

        /* 现代化输入框 - Material Design浮动标签风格 */
        "QLineEdit#modernInput {"
        "    background: rgba(247, 242, 250, 0.8);"           /* MD3 Surface Container */
        "    border: 2px solid #E7E0EC;"                      /* MD3 Outline Variant */
        "    border-radius: 16px;"                             /* 大圆角 */
        "    padding: 16px 20px;"
        "    font-size: 16px;"
        "    font-weight: 400;"
        "    color: #1D1B20;"                                  /* MD3 On Surface */
        "    min-height: 24px;"
        "}"
        "QLineEdit#modernInput:focus {"
        "    background: rgba(255, 255, 255, 0.95);"
        "    border-color: #6750A4;"                           /* MD3 Primary */
        "    box-shadow: 0 0 0 3px rgba(103, 80, 164, 0.1);" /* 焦点光晕 */
        "}"
        "QLineEdit#modernInput::placeholder {"
        "    color: #79747E;"                                  /* MD3 Outline */
        "    font-weight: 400;"
        "}"

        /* 现代化主要按钮 - iOS/Material Design混合风格 */
        "QPushButton#primaryButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #6750A4, stop:1 #5D4E75);"     /* MD3 Primary渐变 */
        "    border: none;"
        "    border-radius: 20px;"                             /* 圆角按钮 */
        "    color: #FFFFFF;"                                  /* 白色文字 */
        "    font-size: 16px;"
        "    font-weight: 600;"                                /* Semibold */
        "    padding: 14px 32px;"
        "    min-width: 120px;"
        "    min-height: 48px;"                                /* 触摸友好尺寸 */
        "    box-shadow: 0 4px 12px rgba(103, 80, 164, 0.3);" /* 彩色阴影 */
        "}"
        "QPushButton#primaryButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #7965AF, stop:1 #6750A4);"     /* 悬停渐变 */
        "    box-shadow: 0 6px 16px rgba(103, 80, 164, 0.4);"
        "    transform: translateY(-2px);"                     /* 悬浮效果 */
        "}"
        "QPushButton#primaryButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #5D4E75, stop:1 #4A3D5C);"     /* 按下渐变 */
        "    box-shadow: 0 2px 8px rgba(103, 80, 164, 0.2);"
        "    transform: translateY(0px);"
        "}"

        /* 现代化次要按钮 */
        "QPushButton#secondaryButton {"
        "    background: rgba(232, 222, 248, 0.8);"            /* MD3 Secondary Container */
        "    border: 2px solid rgba(103, 80, 164, 0.2);"
        "    border-radius: 20px;"
        "    color: #6750A4;"                                  /* MD3 Primary */
        "    font-size: 16px;"
        "    font-weight: 500;"                                /* Medium */
        "    padding: 12px 28px;"
        "    min-width: 100px;"
        "    min-height: 48px;"
        "}"
        "QPushButton#secondaryButton:hover {"
        "    background: rgba(232, 222, 248, 1.0);"
        "    border-color: rgba(103, 80, 164, 0.4);"
        "    box-shadow: 0 2px 8px rgba(103, 80, 164, 0.1);"
        "    transform: translateY(-1px);"
        "}"
        "QPushButton#secondaryButton:pressed {"
        "    background: rgba(221, 208, 240, 1.0);"
        "    border-color: rgba(103, 80, 164, 0.6);"
        "    transform: translateY(0px);"
        "}"

        /* 现代化生物识别图标 */
        "QLabel#biometricIcon {"
        "    color: #79747E;"                                  /* MD3 Outline */
        "    font-size: 24px;"
        "    padding: 12px;"
        "    border-radius: 12px;"
        "    background: rgba(232, 222, 248, 0.5);"            /* 淡紫色背景 */
        "    margin: 8px;"
        "}"
        "QLabel#biometricIcon:hover {"
        "    background: rgba(232, 222, 248, 0.8);"
        "    color: #6750A4;"                                  /* MD3 Primary */
        "}"

        /* 现代化成功提示 */
        "QLabel#successLabel {"
        "    background: rgba(255, 255, 255, 0.95);"
        "    border: none;"
        "    border-radius: 16px;"
        "    color: #1B5E20;"                                  /* 深绿色 */
        "    font-size: 16px;"
        "    font-weight: 500;"
        "    padding: 16px 24px;"
        "    margin: 16px;"
        "    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);"  /* 绿色阴影 */
        "}"

        /* 现代化进度条 */
        "QProgressBar {"
        "    background: rgba(255, 255, 255, 0.8);"
        "    border: none;"
        "    border-radius: 8px;"
        "    text-align: center;"
        "    font-size: 14px;"
        "    font-weight: 500;"
        "    color: #1D1B20;"
        "    margin: 8px 16px;"
        "    min-height: 16px;"
        "}"
        "QProgressBar::chunk {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "                stop:0 #4CAF50, stop:1 #66BB6A);"     /* 绿色渐变 */
        "    border-radius: 8px;"
        "}"
    );
}
