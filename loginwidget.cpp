#include "loginwidget.h"
#include "musicplayerwidget.h"
#include <QApplication>

LoginWidget::LoginWidget(QWidget *parent)
    : QWidget(parent)
    , musicPlayer(nullptr)
    , successTimer(nullptr)
{
    setupUI();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("音乐播放器 - 登录");
    setFixedSize(450, 350);
    
    // 设置样式
    setStyleSheet(
        "LoginWidget {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 #667eea, stop:1 #764ba2);"
        "}"
        "QLabel {"
        "    color: white;"
        "    font-size: 14px;"
        "}"
        "QLabel#titleLabel {"
        "    font-size: 24px;"
        "    font-weight: bold;"
        "    color: white;"
        "}"
        "QLineEdit {"
        "    padding: 8px;"
        "    border: 2px solid #ddd;"
        "    border-radius: 5px;"
        "    font-size: 14px;"
        "    background-color: white;"
        "}"
        "QPushButton {"
        "    padding: 10px 20px;"
        "    border: none;"
        "    border-radius: 5px;"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    color: white;"
        "}"
        "QPushButton#loginButton {"
        "    background-color: #4CAF50;"
        "}"
        "QPushButton#loginButton:hover {"
        "    background-color: #45a049;"
        "}"
        "QPushButton#registerButton {"
        "    background-color: #2196F3;"
        "}"
        "QPushButton#registerButton:hover {"
        "    background-color: #1976D2;"
        "}"
    );
}

LoginWidget::~LoginWidget()
{
}

void LoginWidget::setupUI()
{
    // 创建UI组件
    titleLabel = new QLabel("音乐播放器", this);
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);
    
    usernameLabel = new QLabel("用户名:", this);
    passwordLabel = new QLabel("密码:", this);
    
    usernameEdit = new QLineEdit(this);
    usernameEdit->setPlaceholderText("请输入用户名");
    
    passwordEdit = new QLineEdit(this);
    passwordEdit->setPlaceholderText("请输入密码");
    passwordEdit->setEchoMode(QLineEdit::Password);
    
    loginButton = new QPushButton("登录", this);
    loginButton->setObjectName("loginButton");
    
    registerButton = new QPushButton("注册", this);
    registerButton->setObjectName("registerButton");

    // 创建登录成功提示组件
    successLabel = new QLabel("登录成功！正在进入音乐播放器...", this);
    successLabel->setObjectName("successLabel");
    successLabel->setAlignment(Qt::AlignCenter);
    successLabel->hide();

    loadingBar = new QProgressBar(this);
    loadingBar->setRange(0, 100);
    loadingBar->setValue(0);
    loadingBar->hide();

    // 创建透明度效果和动画
    opacityEffect = new QGraphicsOpacityEffect(this);
    fadeAnimation = new QPropertyAnimation(opacityEffect, "opacity", this);

    // 创建布局
    mainLayout = new QVBoxLayout(this);
    formLayout = new QGridLayout();
    buttonLayout = new QHBoxLayout();
    
    // 设置布局
    mainLayout->addStretch();
    mainLayout->addWidget(titleLabel);
    mainLayout->addSpacing(30);
    
    formLayout->addWidget(usernameLabel, 0, 0);
    formLayout->addWidget(usernameEdit, 0, 1);
    formLayout->addWidget(passwordLabel, 1, 0);
    formLayout->addWidget(passwordEdit, 1, 1);
    
    mainLayout->addLayout(formLayout);
    mainLayout->addSpacing(20);
    
    buttonLayout->addWidget(loginButton);
    buttonLayout->addWidget(registerButton);
    
    mainLayout->addLayout(buttonLayout);
    mainLayout->addStretch();
    
    setLayout(mainLayout);
}

void LoginWidget::setupConnections()
{
    connect(loginButton, &QPushButton::clicked, this, &LoginWidget::onLoginClicked);
    connect(registerButton, &QPushButton::clicked, this, &LoginWidget::onRegisterClicked);
    
    // 回车键登录
    connect(passwordEdit, &QLineEdit::returnPressed, this, &LoginWidget::onLoginClicked);
}

void LoginWidget::onLoginClicked()
{
    QString username = usernameEdit->text().trimmed();
    QString password = passwordEdit->text().trimmed();
    
    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "警告", "用户名和密码不能为空！");
        return;
    }
    
    if (validateLogin(username, password)) {
        // 登录成功，创建并显示音乐播放器界面
        if (!musicPlayer) {
            musicPlayer = new MusicPlayerWidget();
        }
        
        musicPlayer->show();
        this->hide(); // 隐藏登录界面
        
        QMessageBox::information(this, "成功", "登录成功！");
    } else {
        QMessageBox::warning(this, "错误", "用户名或密码错误！");
        passwordEdit->clear();
        passwordEdit->setFocus();
    }
}

void LoginWidget::onRegisterClicked()
{
    QMessageBox::information(this, "注册", "注册功能暂未实现，请使用默认账号：\n用户名: admin\n密码: 123456");
}

bool LoginWidget::validateLogin(const QString &username, const QString &password)
{
    // 简单的验证逻辑，实际项目中应该连接数据库
    return (username == "admin" && password == "123456") ||
           (username == "user" && password == "password");
}
