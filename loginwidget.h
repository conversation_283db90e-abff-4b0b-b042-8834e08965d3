#ifndef LOGINWIDGET_H
#define LOGINWIDGET_H

#include <QWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QGridLayout>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QTimer>
#include <QProgressBar>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

class MusicPlayerWidget;

class LoginWidget : public QWidget
{
    Q_OBJECT

public:
    LoginWidget(QWidget *parent = nullptr);
    ~LoginWidget();

private slots:
    void onLoginClicked();
    void onRegisterClicked();
    void showLoginSuccess();
    void hideLoginSuccess();

private:
    void setupUI();
    void setupConnections();
    bool validateLogin(const QString &username, const QString &password);
    QString getModernLoginStyle();
    
    // UI组件
    QLabel *titleLabel;
    QLabel *usernameLabel;
    QLabel *passwordLabel;
    QLineEdit *usernameEdit;
    QLineEdit *passwordEdit;
    QPushButton *loginButton;
    QPushButton *registerButton;

    // 现代化UI组件
    QLabel *biometricIcon;  // 生物识别图标
    QWidget *loginCard;     // 登录卡片容器

    // 登录成功提示组件
    QLabel *successLabel;
    QProgressBar *loadingBar;
    QPropertyAnimation *fadeAnimation;
    QGraphicsOpacityEffect *opacityEffect;
    QTimer *successTimer;
    
    // 布局
    QVBoxLayout *mainLayout;
    QGridLayout *formLayout;
    QHBoxLayout *buttonLayout;
    
    // 音乐播放器窗口
    MusicPlayerWidget *musicPlayer;
};

#endif // LOGINWIDGET_H
