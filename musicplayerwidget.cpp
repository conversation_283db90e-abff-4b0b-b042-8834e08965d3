#include "musicplayerwidget.h"
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include<QTimer>

MusicPlayerWidget::MusicPlayerWidget(QWidget *parent)
    : QWidget(parent)
    , mediaPlayer(nullptr)
    , playlist(nullptr)
    , currentPlayMode(Sequential)
    , isSeekSliderPressed(false)
    , currentBackgroundIndex(0)
{
    setupUI();
    setupMediaPlayer();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("音乐播放器");
    resize(900, 700);

    // 设置16:9尺寸和网易云音乐风格
    setFixedSize(960, 540); // 16:9比例

    // 加载收藏列表、自定义歌曲名和背景图片
    loadFavorites();
    loadCustomSongNames();
    loadBackgroundImages();
    setDefaultBackground();
    

    
    // 初始化定时器
    updateTimer = new QTimer(this);
    connect(updateTimer, &QTimer::timeout, this, &MusicPlayerWidget::updatePlayTime);
    updateTimer->start(1000); // 每秒更新一次
}

MusicPlayerWidget::~MusicPlayerWidget()
{
    if (mediaPlayer) {
        mediaPlayer->stop();
    }
}

void MusicPlayerWidget::setupUI()
{
    // 创建主布局 - 网易云音乐16:9风格
    mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(8);
    mainLayout->setContentsMargins(12, 12, 12, 12);

    // 顶部歌曲信息区域 - 网易云音乐风格
    QWidget *songInfoCard = new QWidget(this);
    songInfoCard->setObjectName("songInfoCard");
    songInfoCard->setFixedHeight(100);

    QHBoxLayout *cardLayout = new QHBoxLayout(songInfoCard);
    cardLayout->setSpacing(20);

    // 专辑封面占位符 - 网易云音乐风格
    QLabel *albumCover = new QLabel(this);
    albumCover->setFixedSize(70, 70);
    albumCover->setStyleSheet(
        "QLabel {"
        "    background-color: rgba(189, 195, 199, 150);"
        "    border-radius: 8px;"
        "    border: 2px solid rgba(149, 165, 166, 100);"
        "    font-size: 24px;"
        "    color: #7f8c8d;"
        "}"
    );
    albumCover->setAlignment(Qt::AlignCenter);
    albumCover->setText("♪");

    // 歌曲信息文本区域
    QVBoxLayout *songInfoLayout = new QVBoxLayout();
    songInfoLayout->setSpacing(5);

    songTitleLabel = new QLabel("未选择歌曲", this);
    songTitleLabel->setObjectName("songTitleLabel");

    artistLabel = new QLabel("艺术家: 未知", this);
    artistLabel->setObjectName("artistLabel");

    albumLabel = new QLabel("专辑: 未知", this);
    albumLabel->setObjectName("albumLabel");

    songInfoLayout->addWidget(songTitleLabel);
    songInfoLayout->addWidget(artistLabel);
    songInfoLayout->addWidget(albumLabel);
    songInfoLayout->addStretch();

    cardLayout->addWidget(albumCover);
    cardLayout->addLayout(songInfoLayout);
    cardLayout->addStretch();

    mainLayout->addWidget(songInfoCard);
    
    // 中央播放控制区域 - 现代化圆形按钮设计
    QWidget *controlCard = new QWidget(this);
    controlCard->setObjectName("controlCard");
    controlCard->setFixedHeight(100);

    controlLayout = new QHBoxLayout(controlCard);
    controlLayout->setSpacing(20);

    // 创建圆形播放控制按钮
    previousButton = new QPushButton("⏮", this);
    previousButton->setObjectName("controlButton");
    previousButton->setFixedSize(50, 50);

    playPauseButton = new QPushButton("▶", this);
    playPauseButton->setObjectName("playButton");
    playPauseButton->setFixedSize(60, 60);

    nextButton = new QPushButton("⏭", this);
    nextButton->setObjectName("controlButton");
    nextButton->setFixedSize(50, 50);

    stopButton = new QPushButton("⏹", this);
    stopButton->setObjectName("controlButton");
    stopButton->setFixedSize(45, 45);

    playModeButton = new QPushButton("🔁", this);
    playModeButton->setObjectName("modeButton");
    playModeButton->setFixedSize(45, 45);

    controlLayout->addStretch();
    controlLayout->addWidget(playModeButton);
    controlLayout->addWidget(previousButton);
    controlLayout->addWidget(playPauseButton);
    controlLayout->addWidget(nextButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addStretch();

    mainLayout->addWidget(controlCard);
    
    // 进度控制区域 - 现代化进度条设计
    QWidget *progressCard = new QWidget(this);
    progressCard->setObjectName("progressCard");

    QVBoxLayout *progressLayout = new QVBoxLayout(progressCard);
    progressLayout->setSpacing(10);

    seekSlider = new QSlider(Qt::Horizontal, this);
    seekSlider->setRange(0, 0);
    seekSlider->setObjectName("seekSlider");
    seekSlider->setFixedHeight(6);

    seekLayout = new QHBoxLayout();
    currentTimeLabel = new QLabel("00:00", this);
    currentTimeLabel->setObjectName("timeLabel");
    totalTimeLabel = new QLabel("00:00", this);
    totalTimeLabel->setObjectName("timeLabel");

    seekLayout->addWidget(currentTimeLabel);
    seekLayout->addWidget(seekSlider);
    seekLayout->addWidget(totalTimeLabel);

    progressLayout->addLayout(seekLayout);

    // 音量控制区域 - 紧凑设计
    QHBoxLayout *volumeLayout = new QHBoxLayout();
    volumeLayout->setSpacing(15);

    QLabel *volumeIcon = new QLabel("🔊", this);
    volumeIcon->setFixedSize(20, 20);

    volumeSlider = new QSlider(Qt::Horizontal, this);
    volumeSlider->setRange(0, 100);
    volumeSlider->setValue(50);
    volumeSlider->setFixedWidth(120);
    volumeSlider->setFixedHeight(6);
    volumeSlider->setObjectName("volumeSlider");

    volumeLabel = new QLabel("50", this);
    volumeLabel->setObjectName("volumeLabel");
    volumeLabel->setFixedWidth(30);

    volumeUpButton = new QPushButton("🔊", this);
    volumeUpButton->setObjectName("volumeBtn");
    volumeUpButton->setFixedSize(30, 30);

    volumeDownButton = new QPushButton("🔉", this);
    volumeDownButton->setObjectName("volumeBtn");
    volumeDownButton->setFixedSize(30, 30);

    volumeLayout->addStretch();
    volumeLayout->addWidget(volumeDownButton);
    volumeLayout->addWidget(volumeSlider);
    volumeLayout->addWidget(volumeUpButton);
    volumeLayout->addWidget(volumeLabel);
    volumeLayout->addStretch();

    progressLayout->addLayout(volumeLayout);
    mainLayout->addWidget(progressCard);
    
    // 播放列表区域 - 现代化卡片设计
    QWidget *playlistCard = new QWidget(this);
    playlistCard->setObjectName("playlistCard");

    QVBoxLayout *playlistMainLayout = new QVBoxLayout(playlistCard);
    playlistMainLayout->setSpacing(15);

    // 播放列表标题和控制按钮
    QHBoxLayout *playlistHeaderLayout = new QHBoxLayout();
    QLabel *playlistTitle = new QLabel("播放列表", this);
    playlistTitle->setObjectName("playlistTitle");

    // 功能按钮组 - 现代化图标按钮
    addMusicButton = new QPushButton("➕", this);
    addMusicButton->setObjectName("actionButton");
    addMusicButton->setFixedSize(35, 35);
    addMusicButton->setToolTip("添加音乐");

    removeMusicButton = new QPushButton("🗑", this);
    removeMusicButton->setObjectName("actionButton");
    removeMusicButton->setFixedSize(35, 35);
    removeMusicButton->setToolTip("删除音乐");

    favoriteButton = new QPushButton("❤", this);
    favoriteButton->setObjectName("favoriteButton");
    favoriteButton->setFixedSize(35, 35);
    favoriteButton->setToolTip("我喜欢的");

    addBackgroundButton = new QPushButton("🖼", this);
    addBackgroundButton->setObjectName("actionButton");
    addBackgroundButton->setFixedSize(35, 35);
    addBackgroundButton->setToolTip("添加背景图片");

    playlistHeaderLayout->addWidget(playlistTitle);
    playlistHeaderLayout->addStretch();
    playlistHeaderLayout->addWidget(addMusicButton);
    playlistHeaderLayout->addWidget(removeMusicButton);
    playlistHeaderLayout->addWidget(favoriteButton);
    playlistHeaderLayout->addWidget(addBackgroundButton);

    // 播放列表 - 现代化列表设计
    playlistWidget = new QListWidget(this);
    playlistWidget->setObjectName("modernPlaylist");
    playlistWidget->setAlternatingRowColors(false);
    playlistWidget->setMaximumHeight(150); // 限制高度，大约显示5个项目
    playlistWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    playlistWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    playlistWidget->setContextMenuPolicy(Qt::CustomContextMenu);

    playlistMainLayout->addLayout(playlistHeaderLayout);
    playlistMainLayout->addWidget(playlistWidget);
    
    // 完成主布局组装
    mainLayout->addWidget(playlistCard);
    mainLayout->addStretch();

    setLayout(mainLayout);
}

void MusicPlayerWidget::setupMediaPlayer()
{
    // 创建媒体播放器和播放列表
    mediaPlayer = new QMediaPlayer(this);
    playlist = new QMediaPlaylist(this);

    mediaPlayer->setPlaylist(playlist);
    mediaPlayer->setVolume(50);

    // 设置播放模式
    playlist->setPlaybackMode(QMediaPlaylist::Sequential);
}

void MusicPlayerWidget::setupConnections()
{
    // 播放控制连接
    connect(playPauseButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayPauseClicked);
    connect(previousButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPreviousClicked);
    connect(nextButton, &QPushButton::clicked, this, &MusicPlayerWidget::onNextClicked);
    connect(stopButton, &QPushButton::clicked, this, &MusicPlayerWidget::onStopClicked);
    connect(playModeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayModeClicked);

    // 音量控制连接
    connect(volumeSlider, &QSlider::valueChanged, this, &MusicPlayerWidget::onVolumeChanged);
    connect(volumeUpButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeUpClicked);
    connect(volumeDownButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeDownClicked);

    // 播放器状态连接
    connect(mediaPlayer, &QMediaPlayer::stateChanged, this, &MusicPlayerWidget::onStateChanged);
    connect(mediaPlayer, &QMediaPlayer::positionChanged, this, &MusicPlayerWidget::onPositionChanged);
    connect(mediaPlayer, &QMediaPlayer::durationChanged, this, &MusicPlayerWidget::onDurationChanged);
    connect(mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MusicPlayerWidget::onMediaStatusChanged);

    // 进度条控制连接
    connect(seekSlider, &QSlider::sliderPressed, this, &MusicPlayerWidget::onSeekSliderPressed);
    connect(seekSlider, &QSlider::sliderReleased, this, &MusicPlayerWidget::onSeekSliderReleased);
    connect(seekSlider, &QSlider::sliderMoved, this, &MusicPlayerWidget::onSeekSliderMoved);

    // 播放列表连接
    connect(addMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddMusicClicked);
    connect(removeMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onRemoveMusicClicked);
    connect(playlistWidget, &QListWidget::itemClicked, this, &MusicPlayerWidget::onPlaylistItemClicked);
    connect(playlistWidget, &QListWidget::itemDoubleClicked, this, &MusicPlayerWidget::onPlaylistItemDoubleClicked);
    connect(playlistWidget, &QListWidget::customContextMenuRequested, this, &MusicPlayerWidget::onPlaylistItemRightClicked);
    connect(favoriteButton, &QPushButton::clicked, this, &MusicPlayerWidget::onFavoriteClicked);
    connect(addBackgroundButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddBackgroundImageClicked);

    // 播放列表变化连接
    connect(playlist, &QMediaPlaylist::currentIndexChanged, [this](int index) {
        if (index >= 0 && index < playlistWidget->count()) {
            playlistWidget->setCurrentRow(index);
            QListWidgetItem *item = playlistWidget->item(index);
            if (item) {
                QString filePath = item->data(Qt::UserRole).toString();
                QString songTitle = getDisplayName(filePath);
                songTitleLabel->setText(songTitle);
                artistLabel->setText("艺术家: 未知");
                albumLabel->setText("专辑: 未知");

                // 更新播放列表高亮显示
                updatePlaylistHighlight();
            }
        }
    });
}

// 播放控制槽函数
void MusicPlayerWidget::onPlayPauseClicked()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        mediaPlayer->pause();
    } else {
        if (playlist->isEmpty()) {
            QMessageBox::information(this, "提示", "播放列表为空，请先添加音乐文件！");
            return;
        }
        mediaPlayer->play();
    }
}

void MusicPlayerWidget::onPreviousClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->previous();
    }
}

void MusicPlayerWidget::onNextClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->next();
    }
}

void MusicPlayerWidget::onStopClicked()
{
    mediaPlayer->stop();
    seekSlider->setValue(0);
    currentTimeLabel->setText("00:00");
}

// 音量控制槽函数
void MusicPlayerWidget::onVolumeChanged(int volume)
{
    mediaPlayer->setVolume(volume);
    volumeLabel->setText(QString("%1%").arg(volume));
}

void MusicPlayerWidget::onVolumeUpClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMin(100, currentVolume + 10);
    volumeSlider->setValue(newVolume);
}

void MusicPlayerWidget::onVolumeDownClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMax(0, currentVolume - 10);
    volumeSlider->setValue(newVolume);
}

// 播放模式控制
void MusicPlayerWidget::onPlayModeClicked()
{
    switch (currentPlayMode) {
    case Sequential:
        currentPlayMode = Loop;
        playlist->setPlaybackMode(QMediaPlaylist::Loop);
        break;
    case Loop:
        currentPlayMode = CurrentItemInLoop;
        playlist->setPlaybackMode(QMediaPlaylist::CurrentItemInLoop);
        break;
    case CurrentItemInLoop:
        currentPlayMode = Random;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential); // 随机模式用Sequential，在next/previous中处理
        break;
    case Random:
        currentPlayMode = Sequential;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential);
        break;
    }
    updatePlayModeButton();
}

// 播放器状态处理
void MusicPlayerWidget::onStateChanged(QMediaPlayer::State state)
{
    updatePlayPauseButton();
}

void MusicPlayerWidget::onPositionChanged(qint64 position)
{
    if (!isSeekSliderPressed) {
        seekSlider->setValue(position);
    }
    currentTimeLabel->setText(formatTime(position));
}

void MusicPlayerWidget::onDurationChanged(qint64 duration)
{
    seekSlider->setRange(0, duration);
    totalTimeLabel->setText(formatTime(duration));
}

void MusicPlayerWidget::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    if (status == QMediaPlayer::LoadedMedia) {
        // 媒体加载完成
    } else if (status == QMediaPlayer::InvalidMedia) {
        QMessageBox::warning(this, "错误", "无法播放该媒体文件！");
    }
}

// 进度条控制
void MusicPlayerWidget::onSeekSliderPressed()
{
    isSeekSliderPressed = true;
}

void MusicPlayerWidget::onSeekSliderReleased()
{
    isSeekSliderPressed = false;
    mediaPlayer->setPosition(seekSlider->value());
}

void MusicPlayerWidget::onSeekSliderMoved(int position)
{
    currentTimeLabel->setText(formatTime(position));
}

// 播放列表操作
void MusicPlayerWidget::onAddMusicClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(
        this,
        "选择音乐文件",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "音频文件 (*.mp3 *.wav *.flac *.aac *.ogg *.wma);;所有文件 (*.*)"
    );

    for (const QString &fileName : fileNames) {
        addMusicToPlaylist(fileName);
    }
}

void MusicPlayerWidget::onRemoveMusicClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        // 从播放列表中移除
        playlist->removeMedia(currentRow);

        // 从界面列表中移除
        QListWidgetItem *item = playlistWidget->takeItem(currentRow);
        delete item;

        QMessageBox::information(this, "成功", "已删除选中的音乐文件！");
    } else {
        QMessageBox::warning(this, "警告", "请先选择要删除的音乐文件！");
    }
}

// 播放列表单击播放和高亮
void MusicPlayerWidget::onPlaylistItemClicked(QListWidgetItem *item)
{
    if (!item) return;

    int row = playlistWidget->row(item);

    // 切换到选中的歌曲
    playlist->setCurrentIndex(row);

    // 更新歌曲信息显示
    QString filePath = item->data(Qt::UserRole).toString();
    QString songTitle = getDisplayName(filePath);
    songTitleLabel->setText(songTitle);
    artistLabel->setText("艺术家: 未知");
    albumLabel->setText("专辑: 未知");

    // 开始播放
    mediaPlayer->play();
    updatePlayPauseButton();

    // 切换背景图片
    switchBackgroundImage();

    // 更新播放列表高亮显示
    updatePlaylistHighlight();
}

// 播放列表双击播放（保持兼容性）
void MusicPlayerWidget::onPlaylistItemDoubleClicked(QListWidgetItem *item)
{
    // 双击时执行与单击相同的逻辑
    onPlaylistItemClicked(item);
}

// 我喜欢的功能
void MusicPlayerWidget::onFavoriteClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        QListWidgetItem *item = playlistWidget->item(currentRow);
        QString filePath = item->data(Qt::UserRole).toString();

        if (favoriteList.contains(filePath)) {
            favoriteList.removeOne(filePath);
            QMessageBox::information(this, "取消收藏", "已从我喜欢的列表中移除！");
        } else {
            favoriteList.append(filePath);
            QMessageBox::information(this, "添加收藏", "已添加到我喜欢的列表！");
        }
        saveFavorites();
        updatePlaylistDisplay();
    } else {
        QMessageBox::warning(this, "警告", "请先选择要收藏的歌曲！");
    }
}



// 定时器更新
void MusicPlayerWidget::updatePlayTime()
{
    // 这里可以添加额外的定时更新逻辑
    // 目前播放时间更新通过positionChanged信号处理
}

// 辅助方法
void MusicPlayerWidget::updatePlayModeButton()
{
    switch (currentPlayMode) {
    case Sequential:
        playModeButton->setText("🔁");
        playModeButton->setToolTip("顺序播放");
        break;
    case Loop:
        playModeButton->setText("🔄");
        playModeButton->setToolTip("列表循环");
        break;
    case CurrentItemInLoop:
        playModeButton->setText("🔂");
        playModeButton->setToolTip("单曲循环");
        break;
    case Random:
        playModeButton->setText("🔀");
        playModeButton->setToolTip("随机播放");
        break;
    }
}

void MusicPlayerWidget::updatePlayPauseButton()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        playPauseButton->setText("⏸");
    } else {
        playPauseButton->setText("▶");
    }
}

void MusicPlayerWidget::addMusicToPlaylist(const QString &filePath)
{
    // 添加到媒体播放列表
    playlist->addMedia(QUrl::fromLocalFile(filePath));

    // 添加到界面列表，显示正确的歌名
    QString songTitle = getDisplayName(filePath);

    QListWidgetItem *item = new QListWidgetItem(songTitle);
    item->setData(Qt::UserRole, filePath); // 存储完整路径
    item->setToolTip(filePath); // 设置工具提示显示完整路径

    // 如果是收藏的歌曲，添加标记
    if (favoriteList.contains(filePath)) {
        item->setText("❤ " + songTitle);
    }

    playlistWidget->addItem(item);
}

QString MusicPlayerWidget::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}



// 保存收藏列表
void MusicPlayerWidget::saveFavorites()
{
    QJsonArray jsonArray;
    for (const QString &filePath : favoriteList) {
        jsonArray.append(filePath);
    }

    QJsonObject jsonObject;
    jsonObject["favorites"] = jsonArray;

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/favorites.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载收藏列表
void MusicPlayerWidget::loadFavorites()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/favorites.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();
        QJsonArray jsonArray = jsonObject["favorites"].toArray();

        favoriteList.clear();
        for (const QJsonValue &value : jsonArray) {
            favoriteList.append(value.toString());
        }
    }
}

// 提取歌曲标题（优化版）
QString MusicPlayerWidget::extractSongTitle(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();

    // 移除常见的音质标识
    QStringList qualityTags = {"320K", "320k", "FLAC", "flac", "HQ", "hq", "HD", "hd", "MP3", "mp3"};
    for (const QString &tag : qualityTags) {
        baseName = baseName.remove(QRegExp("\\[" + tag + "\\]", Qt::CaseInsensitive));
        baseName = baseName.remove(QRegExp("\\(" + tag + "\\)", Qt::CaseInsensitive));
        baseName = baseName.remove(QRegExp("_" + tag + "_", Qt::CaseInsensitive));
        baseName = baseName.remove(QRegExp("-" + tag + "-", Qt::CaseInsensitive));
    }

    // 移除多余的空格和特殊字符
    baseName = baseName.replace(QRegExp("\\s+"), " ").trimmed();

    // 处理常见的命名格式
    // 格式1: 艺术家 - 歌名
    if (baseName.contains(" - ")) {
        QStringList parts = baseName.split(" - ");
        if (parts.size() >= 2) {
            return parts[1].trimmed();
        }
    }

    // 格式2: 歌名 (艺术家)
    if (baseName.contains("(") && baseName.contains(")")) {
        int startPos = baseName.indexOf("(");
        if (startPos > 0) {
            return baseName.left(startPos).trimmed();
        }
    }

    // 格式3: 数字. 歌名
    QRegExp numberPrefix("^\\d+\\.\\s*");
    if (baseName.contains(numberPrefix)) {
        baseName = baseName.remove(numberPrefix);
    }

    // 格式4: 歌名_艺术家 或 歌名-艺术家
    if (baseName.contains("_")) {
        QStringList parts = baseName.split("_");
        if (parts.size() >= 2) {
            return parts[0].trimmed();
        }
    }

    // 如果没有特殊格式，直接返回处理后的文件名
    return baseName.isEmpty() ? fileInfo.baseName() : baseName;
}

// 获取显示名称（优先使用自定义名称）
QString MusicPlayerWidget::getDisplayName(const QString &filePath)
{
    if (customSongNames.contains(filePath)) {
        return customSongNames[filePath];
    }
    return extractSongTitle(filePath);
}

// 更新播放列表显示
void MusicPlayerWidget::updatePlaylistDisplay()
{
    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        QString filePath = item->data(Qt::UserRole).toString();
        QString songTitle = getDisplayName(filePath);

        if (favoriteList.contains(filePath)) {
            item->setText("❤ " + songTitle);
        } else {
            item->setText(songTitle);
        }
    }
}

// 播放列表右键菜单
void MusicPlayerWidget::onPlaylistItemRightClicked(const QPoint &pos)
{
    QListWidgetItem *item = playlistWidget->itemAt(pos);
    if (!item) return;

    QMenu contextMenu(this);

    QAction *renameAction = contextMenu.addAction("🏷 重命名");
    QAction *favoriteAction = nullptr;

    QString filePath = item->data(Qt::UserRole).toString();
    if (favoriteList.contains(filePath)) {
        favoriteAction = contextMenu.addAction("💔 取消收藏");
    } else {
        favoriteAction = contextMenu.addAction("❤ 添加收藏");
    }

    contextMenu.addSeparator();
    QAction *removeAction = contextMenu.addAction("🗑 删除");

    // 设置QQ音乐风格菜单样式
    contextMenu.setStyleSheet(
        "QMenu {"
        "    background-color: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 8px;"
        "    padding: 8px 0px;"
        "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);"
        "}"
        "QMenu::item {"
        "    padding: 10px 20px;"
        "    border-radius: 0px;"
        "    color: #333333;"
        "    font-size: 13px;"
        "}"
        "QMenu::item:selected {"
        "    background-color: #f5f5f5;"
        "    color: #31c27c;"
        "}"
        "QMenu::separator {"
        "    height: 1px;"
        "    background-color: #f0f0f0;"
        "    margin: 4px 0px;"
        "}"
    );

    QAction *selectedAction = contextMenu.exec(playlistWidget->mapToGlobal(pos));

    if (selectedAction == renameAction) {
        playlistWidget->setCurrentItem(item);
        onRenameSongClicked();
    } else if (selectedAction == favoriteAction) {
        playlistWidget->setCurrentItem(item);
        onFavoriteClicked();
    } else if (selectedAction == removeAction) {
        playlistWidget->setCurrentItem(item);
        onRemoveMusicClicked();
    }
}

// 重命名歌曲
void MusicPlayerWidget::onRenameSongClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, "警告", "请先选择要重命名的歌曲！");
        return;
    }

    QListWidgetItem *item = playlistWidget->item(currentRow);
    QString filePath = item->data(Qt::UserRole).toString();
    QString currentName = getDisplayName(filePath);

    bool ok;
    QString newName = QInputDialog::getText(this, "重命名歌曲",
                                          "请输入新的歌曲名称:",
                                          QLineEdit::Normal,
                                          currentName, &ok);

    if (ok && !newName.trimmed().isEmpty()) {
        newName = newName.trimmed();
        customSongNames[filePath] = newName;
        saveCustomSongNames();
        updatePlaylistDisplay();

        // 如果当前正在播放这首歌，更新显示
        if (playlist->currentIndex() == currentRow) {
            songTitleLabel->setText(newName);
        }

        QMessageBox::information(this, "成功", "歌曲重命名成功！");
    }
}









// 保存自定义歌曲名
void MusicPlayerWidget::saveCustomSongNames()
{
    QJsonObject jsonObject;
    for (auto it = customSongNames.begin(); it != customSongNames.end(); ++it) {
        jsonObject[it.key()] = it.value();
    }

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/custom_song_names.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载自定义歌曲名
void MusicPlayerWidget::loadCustomSongNames()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/custom_song_names.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();

        customSongNames.clear();
        for (auto it = jsonObject.begin(); it != jsonObject.end(); ++it) {
            customSongNames[it.key()] = it.value().toString();
        }
    }
}

// 网易云音乐风格样式（不包含主窗口背景）
QString MusicPlayerWidget::getNetEaseCloudMusicStyle()
{
    return QString(

        /* 歌曲信息卡片 - 半透明浮动效果 */
        "QWidget#songInfoCard {"
        "    background: rgba(255, 255, 255, 180);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    margin: 15px;"
        "    backdrop-filter: blur(10px);"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "    color: #333333;"
        "    margin: 5px;"
        "}"
        "QLabel#artistLabel {"
        "    font-size: 13px;"
        "    color: #666666;"
        "    margin: 2px;"
        "}"
        "QLabel#albumLabel {"
        "    font-size: 12px;"
        "    color: #999999;"
        "    margin: 2px;"
        "}"

        /* 控制按钮卡片 - 半透明浮动效果 */
        "QWidget#controlCard {"
        "    background: rgba(255, 255, 255, 180);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    margin: 15px;"
        "    backdrop-filter: blur(10px);"
        "}"

        /* 保持QQ音乐风格的按钮样式 */
        "QPushButton#controlButton {"
        "    background: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 25px;"
        "    color: #666666;"
        "    font-size: 16px;"
        "    font-weight: normal;"
        "}"
        "QPushButton#controlButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #cccccc;"
        "    color: #333333;"
        "}"
        "QPushButton#controlButton:pressed {"
        "    background: #eeeeee;"
        "    border-color: #bbbbbb;"
        "}"
        "QPushButton#playButton {"
        "    background: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 30px;"
        "    color: #666666;"
        "    font-size: 20px;"
        "    font-weight: normal;"
        "}"
        "QPushButton#playButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #cccccc;"
        "    color: #333333;"
        "}"
        "QPushButton#playButton:pressed {"
        "    background: #eeeeee;"
        "    border-color: #bbbbbb;"
        "}"
        "QPushButton#modeButton {"
        "    background: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 22px;"
        "    color: #666666;"
        "    font-size: 14px;"
        "    font-weight: normal;"
        "}"
        "QPushButton#modeButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #cccccc;"
        "    color: #333333;"
        "}"
        "QPushButton#modeButton:pressed {"
        "    background: #eeeeee;"
        "    border-color: #bbbbbb;"
        "}"

        /* 进度条和音量控制卡片 - 半透明浮动效果 */
        "QWidget#progressCard {"
        "    background: rgba(255, 255, 255, 180);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    padding: 15px;"
        "    margin: 15px;"
        "    backdrop-filter: blur(10px);"
        "}"
        "QLabel#timeLabel {"
        "    color: #666666;"
        "    font-size: 12px;"
        "    font-weight: normal;"
        "}"

        /* 保持QQ音乐风格的滑块样式 */
        "QSlider#seekSlider::groove:horizontal {"
        "    background: #e0e0e0;"
        "    height: 4px;"
        "    border-radius: 2px;"
        "}"
        "QSlider#seekSlider::sub-page:horizontal {"
        "    background: #31c27c;"
        "    border-radius: 2px;"
        "}"
        "QSlider#seekSlider::handle:horizontal {"
        "    background: white;"
        "    border: 2px solid #31c27c;"
        "    width: 14px;"
        "    height: 14px;"
        "    border-radius: 7px;"
        "    margin: -5px 0;"
        "}"
        "QSlider#seekSlider::handle:horizontal:hover {"
        "    background: #f5f5f5;"
        "    border-color: #2bb673;"
        "}"
        "QSlider#volumeSlider::groove:horizontal {"
        "    background: #e0e0e0;"
        "    height: 4px;"
        "    border-radius: 2px;"
        "}"
        "QSlider#volumeSlider::sub-page:horizontal {"
        "    background: #31c27c;"
        "    border-radius: 2px;"
        "}"
        "QSlider#volumeSlider::handle:horizontal {"
        "    background: white;"
        "    border: 2px solid #31c27c;"
        "    width: 12px;"
        "    height: 12px;"
        "    border-radius: 6px;"
        "    margin: -4px 0;"
        "}"
        "QSlider#volumeSlider::handle:horizontal:hover {"
        "    background: #f5f5f5;"
        "    border-color: #2bb673;"
        "}"
        "QLabel#volumeLabel {"
        "    color: #666666;"
        "    font-size: 12px;"
        "    font-weight: normal;"
        "}"
        "QPushButton#volumeBtn {"
        "    background: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 15px;"
        "    color: #666666;"
        "    font-size: 12px;"
        "    font-weight: normal;"
        "}"
        "QPushButton#volumeBtn:hover {"
        "    background: #f5f5f5;"
        "    border-color: #cccccc;"
        "    color: #333333;"
        "}"
        "QPushButton#volumeBtn:pressed {"
        "    background: #eeeeee;"
        "    border-color: #bbbbbb;"
        "}"

        /* 播放列表卡片 - 半透明浮动效果 */
        "QWidget#playlistCard {"
        "    background: rgba(255, 255, 255, 180);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    padding: 15px;"
        "    margin: 15px;"
        "    backdrop-filter: blur(10px);"
        "}"
        "QLabel#playlistTitle {"
        "    color: #333333;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    margin-bottom: 10px;"
        "}"

        /* 保持QQ音乐风格的功能按钮 */
        "QPushButton#actionButton {"
        "    background: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 17px;"
        "    color: #666666;"
        "    font-size: 14px;"
        "    font-weight: normal;"
        "    padding: 6px 8px;"
        "}"
        "QPushButton#actionButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #cccccc;"
        "    color: #333333;"
        "}"
        "QPushButton#actionButton:pressed {"
        "    background: #eeeeee;"
        "    border-color: #bbbbbb;"
        "}"
        "QPushButton#favoriteButton {"
        "    background: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 17px;"
        "    color: #ff6b6b;"
        "    font-size: 14px;"
        "    font-weight: normal;"
        "    padding: 6px 8px;"
        "}"
        "QPushButton#favoriteButton:hover {"
        "    background: #fff5f5;"
        "    border-color: #ff6b6b;"
        "    color: #ff5252;"
        "}"
        "QPushButton#favoriteButton:pressed {"
        "    background: #ffebee;"
        "    border-color: #ff5252;"
        "}"

        /* 半透明播放列表 - 增强高亮显示 */
        "QListWidget#modernPlaylist {"
        "    background: rgba(255, 255, 255, 150);"
        "    border: 1px solid rgba(255, 255, 255, 80);"
        "    border-radius: 8px;"
        "    color: #333333;"
        "    font-size: 13px;"
        "    outline: none;"
        "    backdrop-filter: blur(5px);"
        "}"
        "QListWidget#modernPlaylist::item {"
        "    padding: 12px 15px;"
        "    border-bottom: 1px solid rgba(255, 255, 255, 50);"
        "    border-radius: 4px;"
        "    margin: 2px;"
        "}"
        "QListWidget#modernPlaylist::item:selected {"
        "    background: rgba(49, 194, 124, 200);"
        "    color: white;"
        "    font-weight: bold;"
        "    border-left: 4px solid #31c27c;"
        "    border-radius: 6px;"
        "}"
        "QListWidget#modernPlaylist::item:hover {"
        "    background: rgba(255, 255, 255, 120);"
        "    color: #333333;"
        "    border-radius: 4px;"
        "}"
        "QListWidget#modernPlaylist::item:alternate {"
        "    background: rgba(255, 255, 255, 100);"
        "}"
    );
}

// 更新播放列表高亮显示
void MusicPlayerWidget::updatePlaylistHighlight()
{
    int currentIndex = playlist->currentIndex();

    // 清除所有项目的选中状态
    playlistWidget->clearSelection();

    // 设置当前播放项目为选中状态
    if (currentIndex >= 0 && currentIndex < playlistWidget->count()) {
        QListWidgetItem *currentItem = playlistWidget->item(currentIndex);
        if (currentItem) {
            playlistWidget->setCurrentItem(currentItem);
            currentItem->setSelected(true);
        }
    }
}

// 添加背景图片
void MusicPlayerWidget::onAddBackgroundImageClicked()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择背景图片",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.webp);;所有文件 (*.*)"
    );

    if (!fileName.isEmpty()) {
        backgroundImages.append(fileName);
        saveBackgroundImages();

        // 立即应用新添加的背景图片
        applyBackgroundImage(fileName);
        currentBackgroundIndex = backgroundImages.size() - 1;

        QMessageBox::information(this, "成功", "背景图片已添加并应用！\n切换歌曲时会自动使用新背景。");
    }
}

// 切换背景图片
void MusicPlayerWidget::switchBackgroundImage()
{
    if (!backgroundImages.isEmpty()) {
        currentBackgroundIndex = (currentBackgroundIndex + 1) % backgroundImages.size();
        applyBackgroundImage(backgroundImages[currentBackgroundIndex]);
    }
}

// 加载背景图片列表
void MusicPlayerWidget::loadBackgroundImages()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/background_images.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonArray jsonArray = doc.array();

        backgroundImages.clear();
        for (const QJsonValue &value : jsonArray) {
            QString imagePath = value.toString();
            // 检查文件是否存在
            if (QFile::exists(imagePath)) {
                backgroundImages.append(imagePath);
            }
        }
    }

    // 如果没有背景图片，添加一些默认的渐变背景
    if (backgroundImages.isEmpty()) {
        backgroundImages << "default_gradient_1"
                        << "default_gradient_2"
                        << "default_gradient_3"
                        << "default_gradient_4";
    }
}

// 应用背景图片
void MusicPlayerWidget::applyBackgroundImage(const QString &imagePath)
{
    currentBackgroundPath = imagePath;

    QString backgroundStyle;

    if (imagePath.startsWith("default_gradient_")) {
        // 默认渐变背景
        if (imagePath == "default_gradient_1") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(135, 206, 250, 200), stop:1 rgba(176, 224, 230, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        } else if (imagePath == "default_gradient_2") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(255, 182, 193, 200), stop:1 rgba(255, 218, 185, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        } else if (imagePath == "default_gradient_3") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(221, 160, 221, 200), stop:1 rgba(238, 130, 238, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        } else if (imagePath == "default_gradient_4") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(152, 251, 152, 200), stop:1 rgba(144, 238, 144, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        }
    } else {
        // 图片背景
        QString normalizedPath = QDir::toNativeSeparators(imagePath);
        backgroundStyle = QString(
            "MusicPlayerWidget {"
            "    background-image: url(%1);"
            "    background-position: center;"
            "    background-repeat: no-repeat;"
            "    background-size: cover;"
            "    border-radius: 8px;"
            "    color: #333333;"
            "}"
        ).arg(normalizedPath);
    }

    // 应用背景样式，同时保持组件样式
    QString fullStyle = backgroundStyle + getNetEaseCloudMusicStyle();
    setStyleSheet(fullStyle);
}

// 设置默认背景
void MusicPlayerWidget::setDefaultBackground()
{
    if (!backgroundImages.isEmpty()) {
        applyBackgroundImage(backgroundImages[0]);
    } else {
        // 如果没有背景图片，使用默认的网易云音乐风格
        QString defaultStyle =
            "MusicPlayerWidget {"
            "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
            "                stop:0 #f5f5f5, stop:1 #ffffff);"
            "    border-radius: 8px;"
            "    color: #333333;"
            "}" + getNetEaseCloudMusicStyle();
        setStyleSheet(defaultStyle);
    }
}

// 保存背景图片列表
void MusicPlayerWidget::saveBackgroundImages()
{
    QJsonArray jsonArray;
    for (const QString &imagePath : backgroundImages) {
        // 只保存真实的图片文件路径，不保存默认渐变
        if (!imagePath.startsWith("default_gradient_")) {
            jsonArray.append(imagePath);
        }
    }

    QJsonDocument doc(jsonArray);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/background_images.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}


