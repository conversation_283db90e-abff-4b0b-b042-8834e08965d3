#include "musicplayerwidget.h"
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>

MusicPlayerWidget::MusicPlayerWidget(QWidget *parent)
    : QWidget(parent)
    , mediaPlayer(nullptr)
    , playlist(nullptr)
    , currentPlayMode(Sequential)
    , isSeekSliderPressed(false)
    , currentThemeIndex(0)
    , currentThemeIndex(0)
{
    setupUI();
    setupMediaPlayer();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("音乐播放器");
    resize(900, 700);

    // 加载收藏列表和主题
    loadFavorites();
    loadThemeImages();
    
    // 设置样式（透明化主题）
    setStyleSheet(
        "MusicPlayerWidget {"
        "    background-color: rgba(240, 240, 240, 180);"
        "    border-radius: 10px;"
        "}"
        "QLabel {"
        "    color: #333;"
        "    font-size: 12px;"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    color: #2196F3;"
        "}"
        "QPushButton {"
        "    padding: 8px 16px;"
        "    border: 1px solid #ddd;"
        "    border-radius: 6px;"
        "    background-color: rgba(255, 255, 255, 200);"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(230, 243, 255, 220);"
        "    border-color: #2196F3;"
        "    transform: scale(1.05);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgba(204, 231, 255, 240);"
        "}"
        "QPushButton#favoriteButton {"
        "    background-color: rgba(255, 193, 7, 200);"
        "    color: white;"
        "}"
        "QPushButton#favoriteButton:hover {"
        "    background-color: rgba(255, 193, 7, 240);"
        "}"
        "QPushButton#themeButton {"
        "    background-color: rgba(156, 39, 176, 200);"
        "    color: white;"
        "}"
        "QPushButton#themeButton:hover {"
        "    background-color: rgba(156, 39, 176, 240);"
        "}"
        "QSlider::groove:horizontal {"
        "    border: 1px solid #bbb;"
        "    background: white;"
        "    height: 10px;"
        "    border-radius: 4px;"
        "}"
        "QSlider::sub-page:horizontal {"
        "    background: #2196F3;"
        "    border: 1px solid #777;"
        "    height: 10px;"
        "    border-radius: 4px;"
        "}"
        "QSlider::add-page:horizontal {"
        "    background: #fff;"
        "    border: 1px solid #777;"
        "    height: 10px;"
        "    border-radius: 4px;"
        "}"
        "QSlider::handle:horizontal {"
        "    background: #2196F3;"
        "    border: 1px solid #5c5c5c;"
        "    width: 18px;"
        "    margin: -2px 0;"
        "    border-radius: 3px;"
        "}"
        "QListWidget {"
        "    border: 1px solid #ddd;"
        "    background-color: rgba(255, 255, 255, 180);"
        "    alternate-background-color: rgba(249, 249, 249, 180);"
        "    border-radius: 5px;"
        "    max-height: 150px;"
        "}"
        "QListWidget::item {"
        "    padding: 8px;"
        "    border-bottom: 1px solid #eee;"
        "    border-radius: 3px;"
        "    margin: 2px;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: rgba(33, 150, 243, 200);"
        "    color: white;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: rgba(33, 150, 243, 100);"
        "}"
        "QScrollArea {"
        "    border: none;"
        "    background-color: transparent;"
        "}"
    );
    
    // 初始化定时器
    updateTimer = new QTimer(this);
    connect(updateTimer, &QTimer::timeout, this, &MusicPlayerWidget::updatePlayTime);
    updateTimer->start(1000); // 每秒更新一次
}

MusicPlayerWidget::~MusicPlayerWidget()
{
    if (mediaPlayer) {
        mediaPlayer->stop();
    }
}

void MusicPlayerWidget::setupUI()
{
    // 创建主布局
    mainLayout = new QVBoxLayout(this);
    
    // 歌曲信息区域
    songTitleLabel = new QLabel("未选择歌曲", this);
    songTitleLabel->setObjectName("songTitleLabel");
    songTitleLabel->setAlignment(Qt::AlignCenter);
    
    artistLabel = new QLabel("艺术家: 未知", this);
    artistLabel->setAlignment(Qt::AlignCenter);
    
    albumLabel = new QLabel("专辑: 未知", this);
    albumLabel->setAlignment(Qt::AlignCenter);
    
    infoLayout = new QHBoxLayout();
    QVBoxLayout *songInfoLayout = new QVBoxLayout();
    songInfoLayout->addWidget(songTitleLabel);
    songInfoLayout->addWidget(artistLabel);
    songInfoLayout->addWidget(albumLabel);
    infoLayout->addLayout(songInfoLayout);
    
    // 播放控制区域
    previousButton = new QPushButton("上一曲", this);
    playPauseButton = new QPushButton("播放", this);
    nextButton = new QPushButton("下一曲", this);
    stopButton = new QPushButton("停止", this);
    playModeButton = new QPushButton("顺序播放", this);
    
    controlLayout = new QHBoxLayout();
    controlLayout->addStretch();
    controlLayout->addWidget(previousButton);
    controlLayout->addWidget(playPauseButton);
    controlLayout->addWidget(nextButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addWidget(playModeButton);
    controlLayout->addStretch();
    
    // 进度控制区域
    seekSlider = new QSlider(Qt::Horizontal, this);
    seekSlider->setRange(0, 0);
    currentTimeLabel = new QLabel("00:00", this);
    totalTimeLabel = new QLabel("00:00", this);
    
    seekLayout = new QHBoxLayout();
    seekLayout->addWidget(currentTimeLabel);
    seekLayout->addWidget(seekSlider);
    seekLayout->addWidget(totalTimeLabel);
    
    // 音量控制区域
    volumeSlider = new QSlider(Qt::Horizontal, this);
    volumeSlider->setRange(0, 100);
    volumeSlider->setValue(50);
    volumeSlider->setMaximumWidth(150);
    
    volumeLabel = new QLabel("音量: 50", this);
    volumeUpButton = new QPushButton("音量+", this);
    volumeDownButton = new QPushButton("音量-", this);
    
    volumeLayout = new QHBoxLayout();
    volumeLayout->addStretch();
    volumeLayout->addWidget(new QLabel("音量控制:", this));
    volumeLayout->addWidget(volumeDownButton);
    volumeLayout->addWidget(volumeSlider);
    volumeLayout->addWidget(volumeUpButton);
    volumeLayout->addWidget(volumeLabel);
    volumeLayout->addStretch();
    
    // 播放列表区域（带滚动，最多显示5个）
    playlistWidget = new QListWidget(this);
    playlistWidget->setAlternatingRowColors(true);
    playlistWidget->setMaximumHeight(150); // 限制高度，大约显示5个项目
    playlistWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动区域
    playlistScrollArea = new QScrollArea(this);
    playlistScrollArea->setWidget(playlistWidget);
    playlistScrollArea->setWidgetResizable(true);
    playlistScrollArea->setMaximumHeight(150);

    addMusicButton = new QPushButton("添加音乐", this);
    removeMusicButton = new QPushButton("删除音乐", this);
    favoriteButton = new QPushButton("❤ 我喜欢的", this);
    favoriteButton->setObjectName("favoriteButton");
    themeButton = new QPushButton("🎨 主题切换", this);
    themeButton->setObjectName("themeButton");
    addThemeButton = new QPushButton("📷 添加主题", this);
    addThemeButton->setObjectName("addThemeButton");

    playlistControlLayout = new QHBoxLayout();
    playlistControlLayout->addWidget(addMusicButton);
    playlistControlLayout->addWidget(removeMusicButton);
    playlistControlLayout->addWidget(favoriteButton);
    playlistControlLayout->addWidget(themeButton);
    playlistControlLayout->addWidget(addThemeButton);
    playlistControlLayout->addStretch();
    
    // 组装主布局
    mainLayout->addLayout(infoLayout);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(controlLayout);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(seekLayout);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(volumeLayout);
    mainLayout->addSpacing(20);
    mainLayout->addWidget(new QLabel("播放列表:", this));
    mainLayout->addLayout(playlistControlLayout);
    mainLayout->addWidget(playlistWidget); // 直接使用playlistWidget，已设置最大高度
    
    setLayout(mainLayout);
}

void MusicPlayerWidget::setupMediaPlayer()
{
    // 创建媒体播放器和播放列表
    mediaPlayer = new QMediaPlayer(this);
    playlist = new QMediaPlaylist(this);

    mediaPlayer->setPlaylist(playlist);
    mediaPlayer->setVolume(50);

    // 设置播放模式
    playlist->setPlaybackMode(QMediaPlaylist::Sequential);
}

void MusicPlayerWidget::setupConnections()
{
    // 播放控制连接
    connect(playPauseButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayPauseClicked);
    connect(previousButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPreviousClicked);
    connect(nextButton, &QPushButton::clicked, this, &MusicPlayerWidget::onNextClicked);
    connect(stopButton, &QPushButton::clicked, this, &MusicPlayerWidget::onStopClicked);
    connect(playModeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayModeClicked);

    // 音量控制连接
    connect(volumeSlider, &QSlider::valueChanged, this, &MusicPlayerWidget::onVolumeChanged);
    connect(volumeUpButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeUpClicked);
    connect(volumeDownButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeDownClicked);

    // 播放器状态连接
    connect(mediaPlayer, &QMediaPlayer::stateChanged, this, &MusicPlayerWidget::onStateChanged);
    connect(mediaPlayer, &QMediaPlayer::positionChanged, this, &MusicPlayerWidget::onPositionChanged);
    connect(mediaPlayer, &QMediaPlayer::durationChanged, this, &MusicPlayerWidget::onDurationChanged);
    connect(mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MusicPlayerWidget::onMediaStatusChanged);

    // 进度条控制连接
    connect(seekSlider, &QSlider::sliderPressed, this, &MusicPlayerWidget::onSeekSliderPressed);
    connect(seekSlider, &QSlider::sliderReleased, this, &MusicPlayerWidget::onSeekSliderReleased);
    connect(seekSlider, &QSlider::sliderMoved, this, &MusicPlayerWidget::onSeekSliderMoved);

    // 播放列表连接
    connect(addMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddMusicClicked);
    connect(removeMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onRemoveMusicClicked);
    connect(playlistWidget, &QListWidget::itemDoubleClicked, this, &MusicPlayerWidget::onPlaylistItemDoubleClicked);
    connect(favoriteButton, &QPushButton::clicked, this, &MusicPlayerWidget::onFavoriteClicked);
    connect(themeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onThemeClicked);
    connect(addThemeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddThemeImageClicked);

    // 播放列表变化连接
    connect(playlist, &QMediaPlaylist::currentIndexChanged, [this](int index) {
        if (index >= 0 && index < playlistWidget->count()) {
            playlistWidget->setCurrentRow(index);
            QListWidgetItem *item = playlistWidget->item(index);
            if (item) {
                QString filePath = item->data(Qt::UserRole).toString();
                QString songTitle = extractSongTitle(filePath);
                songTitleLabel->setText(songTitle);
                artistLabel->setText("艺术家: 未知");
                albumLabel->setText("专辑: 未知");

                // 切换歌曲时自动切换主题
                switchTheme();
            }
        }
    });
}

// 播放控制槽函数
void MusicPlayerWidget::onPlayPauseClicked()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        mediaPlayer->pause();
    } else {
        if (playlist->isEmpty()) {
            QMessageBox::information(this, "提示", "播放列表为空，请先添加音乐文件！");
            return;
        }
        mediaPlayer->play();
    }
}

void MusicPlayerWidget::onPreviousClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->previous();
    }
}

void MusicPlayerWidget::onNextClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->next();
    }
}

void MusicPlayerWidget::onStopClicked()
{
    mediaPlayer->stop();
    seekSlider->setValue(0);
    currentTimeLabel->setText("00:00");
}

// 音量控制槽函数
void MusicPlayerWidget::onVolumeChanged(int volume)
{
    mediaPlayer->setVolume(volume);
    volumeLabel->setText(QString("音量: %1").arg(volume));
}

void MusicPlayerWidget::onVolumeUpClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMin(100, currentVolume + 10);
    volumeSlider->setValue(newVolume);
}

void MusicPlayerWidget::onVolumeDownClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMax(0, currentVolume - 10);
    volumeSlider->setValue(newVolume);
}

// 播放模式控制
void MusicPlayerWidget::onPlayModeClicked()
{
    switch (currentPlayMode) {
    case Sequential:
        currentPlayMode = Loop;
        playlist->setPlaybackMode(QMediaPlaylist::Loop);
        break;
    case Loop:
        currentPlayMode = CurrentItemInLoop;
        playlist->setPlaybackMode(QMediaPlaylist::CurrentItemInLoop);
        break;
    case CurrentItemInLoop:
        currentPlayMode = Random;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential); // 随机模式用Sequential，在next/previous中处理
        break;
    case Random:
        currentPlayMode = Sequential;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential);
        break;
    }
    updatePlayModeButton();
}

// 播放器状态处理
void MusicPlayerWidget::onStateChanged(QMediaPlayer::State state)
{
    updatePlayPauseButton();
}

void MusicPlayerWidget::onPositionChanged(qint64 position)
{
    if (!isSeekSliderPressed) {
        seekSlider->setValue(position);
    }
    currentTimeLabel->setText(formatTime(position));
}

void MusicPlayerWidget::onDurationChanged(qint64 duration)
{
    seekSlider->setRange(0, duration);
    totalTimeLabel->setText(formatTime(duration));
}

void MusicPlayerWidget::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    if (status == QMediaPlayer::LoadedMedia) {
        // 媒体加载完成
    } else if (status == QMediaPlayer::InvalidMedia) {
        QMessageBox::warning(this, "错误", "无法播放该媒体文件！");
    }
}

// 进度条控制
void MusicPlayerWidget::onSeekSliderPressed()
{
    isSeekSliderPressed = true;
}

void MusicPlayerWidget::onSeekSliderReleased()
{
    isSeekSliderPressed = false;
    mediaPlayer->setPosition(seekSlider->value());
}

void MusicPlayerWidget::onSeekSliderMoved(int position)
{
    currentTimeLabel->setText(formatTime(position));
}

// 播放列表操作
void MusicPlayerWidget::onAddMusicClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(
        this,
        "选择音乐文件",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "音频文件 (*.mp3 *.wav *.flac *.aac *.ogg *.wma);;所有文件 (*.*)"
    );

    for (const QString &fileName : fileNames) {
        addMusicToPlaylist(fileName);
    }
}

void MusicPlayerWidget::onRemoveMusicClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        // 从播放列表中移除
        playlist->removeMedia(currentRow);

        // 从界面列表中移除
        QListWidgetItem *item = playlistWidget->takeItem(currentRow);
        delete item;

        QMessageBox::information(this, "成功", "已删除选中的音乐文件！");
    } else {
        QMessageBox::warning(this, "警告", "请先选择要删除的音乐文件！");
    }
}

void MusicPlayerWidget::onPlaylistItemDoubleClicked(QListWidgetItem *item)
{
    int row = playlistWidget->row(item);
    playlist->setCurrentIndex(row);
    mediaPlayer->play();
}

// 我喜欢的功能
void MusicPlayerWidget::onFavoriteClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        QListWidgetItem *item = playlistWidget->item(currentRow);
        QString filePath = item->data(Qt::UserRole).toString();

        if (favoriteList.contains(filePath)) {
            favoriteList.removeOne(filePath);
            QMessageBox::information(this, "取消收藏", "已从我喜欢的列表中移除！");
        } else {
            favoriteList.append(filePath);
            QMessageBox::information(this, "添加收藏", "已添加到我喜欢的列表！");
        }
        saveFavorites();
        updatePlaylistDisplay();
    } else {
        QMessageBox::warning(this, "警告", "请先选择要收藏的歌曲！");
    }
}

// 主题切换功能
void MusicPlayerWidget::onThemeClicked()
{
    switchTheme();
    QMessageBox::information(this, "主题切换", "主题已切换！");
}

// 添加主题图片
void MusicPlayerWidget::onAddThemeImageClicked()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择主题图片",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*.*)"
    );

    if (!fileName.isEmpty()) {
        themeImages.append(fileName);
        QMessageBox::information(this, "成功", "主题图片已添加！");
    }
}

// 定时器更新
void MusicPlayerWidget::updatePlayTime()
{
    // 这里可以添加额外的定时更新逻辑
    // 目前播放时间更新通过positionChanged信号处理
}

// 辅助方法
void MusicPlayerWidget::updatePlayModeButton()
{
    switch (currentPlayMode) {
    case Sequential:
        playModeButton->setText("顺序播放");
        break;
    case Loop:
        playModeButton->setText("列表循环");
        break;
    case CurrentItemInLoop:
        playModeButton->setText("单曲循环");
        break;
    case Random:
        playModeButton->setText("随机播放");
        break;
    }
}

void MusicPlayerWidget::updatePlayPauseButton()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        playPauseButton->setText("暂停");
    } else {
        playPauseButton->setText("播放");
    }
}

void MusicPlayerWidget::addMusicToPlaylist(const QString &filePath)
{
    // 添加到媒体播放列表
    playlist->addMedia(QUrl::fromLocalFile(filePath));

    // 添加到界面列表，显示正确的歌名
    QString songTitle = extractSongTitle(filePath);

    QListWidgetItem *item = new QListWidgetItem(songTitle);
    item->setData(Qt::UserRole, filePath); // 存储完整路径
    item->setToolTip(filePath); // 设置工具提示显示完整路径

    // 如果是收藏的歌曲，添加标记
    if (favoriteList.contains(filePath)) {
        item->setText("❤ " + songTitle);
    }

    playlistWidget->addItem(item);
}

QString MusicPlayerWidget::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}

// 主题切换实现
void MusicPlayerWidget::switchTheme()
{
    if (!themeImages.isEmpty()) {
        currentThemeIndex = (currentThemeIndex + 1) % themeImages.size();
        applyTheme(themeImages[currentThemeIndex]);
    }
}

// 加载主题图片
void MusicPlayerWidget::loadThemeImages()
{
    // 添加一些默认主题
    themeImages << ":/themes/default.jpg"
                << ":/themes/blue.jpg"
                << ":/themes/purple.jpg"
                << ":/themes/green.jpg";

    // 如果没有默认主题，创建纯色主题
    if (themeImages.isEmpty()) {
        themeImages << "default_blue" << "default_purple" << "default_green" << "default_orange";
    }
}

// 应用主题
void MusicPlayerWidget::applyTheme(const QString &imagePath)
{
    currentThemePath = imagePath;

    QString styleSheet;
    if (imagePath.startsWith("default_")) {
        // 纯色主题
        QString color = imagePath.split("_")[1];
        if (color == "blue") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(33, 150, 243, 180), stop:1 rgba(3, 169, 244, 180));"
                "    border-radius: 10px;"
                "}"
            );
        } else if (color == "purple") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(156, 39, 176, 180), stop:1 rgba(233, 30, 99, 180));"
                "    border-radius: 10px;"
                "}"
            );
        } else if (color == "green") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(76, 175, 80, 180), stop:1 rgba(139, 195, 74, 180));"
                "    border-radius: 10px;"
                "}"
            );
        } else if (color == "orange") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(255, 152, 0, 180), stop:1 rgba(255, 193, 7, 180));"
                "    border-radius: 10px;"
                "}"
            );
        }
    } else {
        // 图片主题
        styleSheet = QString(
            "MusicPlayerWidget {"
            "    background-image: url(%1);"
            "    background-position: center;"
            "    background-repeat: no-repeat;"
            "    background-attachment: fixed;"
            "    border-radius: 10px;"
            "}"
            "MusicPlayerWidget::before {"
            "    content: '';"
            "    position: absolute;"
            "    top: 0; left: 0; right: 0; bottom: 0;"
            "    background-color: rgba(0, 0, 0, 0.25);"
            "    border-radius: 10px;"
            "}"
        ).arg(imagePath);
    }

    // 保持其他样式不变，只更新背景
    setStyleSheet(styleSheet +
        "QLabel {"
        "    color: #333;"
        "    font-size: 12px;"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    color: #2196F3;"
        "}"
        "QPushButton {"
        "    padding: 8px 16px;"
        "    border: 1px solid #ddd;"
        "    border-radius: 6px;"
        "    background-color: rgba(255, 255, 255, 190);"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(230, 243, 255, 220);"
        "    border-color: #2196F3;"
        "}"
        "QPushButton#favoriteButton {"
        "    background-color: rgba(255, 193, 7, 190);"
        "    color: white;"
        "}"
        "QPushButton#themeButton {"
        "    background-color: rgba(156, 39, 176, 190);"
        "    color: white;"
        "}"
        "QListWidget {"
        "    border: 1px solid #ddd;"
        "    background-color: rgba(255, 255, 255, 180);"
        "    border-radius: 5px;"
        "    max-height: 150px;"
        "}"
        "QListWidget::item {"
        "    padding: 8px;"
        "    border-bottom: 1px solid #eee;"
        "    border-radius: 3px;"
        "    margin: 2px;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: rgba(33, 150, 243, 200);"
        "    color: white;"
        "}"
    );
}

// 保存收藏列表
void MusicPlayerWidget::saveFavorites()
{
    QJsonArray jsonArray;
    for (const QString &filePath : favoriteList) {
        jsonArray.append(filePath);
    }

    QJsonObject jsonObject;
    jsonObject["favorites"] = jsonArray;

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/favorites.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载收藏列表
void MusicPlayerWidget::loadFavorites()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/favorites.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();
        QJsonArray jsonArray = jsonObject["favorites"].toArray();

        favoriteList.clear();
        for (const QJsonValue &value : jsonArray) {
            favoriteList.append(value.toString());
        }
    }
}

// 提取歌曲标题
QString MusicPlayerWidget::extractSongTitle(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();

    // 尝试从文件名中提取歌曲信息
    // 处理常见的命名格式：艺术家 - 歌名.mp3
    if (baseName.contains(" - ")) {
        QStringList parts = baseName.split(" - ");
        if (parts.size() >= 2) {
            return parts[1].trimmed(); // 返回歌名部分
        }
    }

    // 如果没有特殊格式，直接返回文件名
    return baseName;
}

// 更新播放列表显示
void MusicPlayerWidget::updatePlaylistDisplay()
{
    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        QString filePath = item->data(Qt::UserRole).toString();
        QString songTitle = extractSongTitle(filePath);

        if (favoriteList.contains(filePath)) {
            item->setText("❤ " + songTitle);
        } else {
            item->setText(songTitle);
        }
    }
}
