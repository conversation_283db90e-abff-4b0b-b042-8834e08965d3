#include "musicplayerwidget.h"
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>

MusicPlayerWidget::MusicPlayerWidget(QWidget *parent)
    : QWidget(parent)
    , mediaPlayer(nullptr)
    , playlist(nullptr)
    , currentPlayMode(Sequential)
    , isSeekSliderPressed(false)
    , currentThemeIndex(0)
{
    setupUI();
    setupMediaPlayer();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("音乐播放器");
    resize(900, 700);

    // 加载收藏列表和主题
    loadFavorites();
    loadThemeImages();
    
    // 设置现代化样式（透明化主题）
    setStyleSheet(
        "MusicPlayerWidget {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 rgba(30, 30, 30, 200), stop:1 rgba(50, 50, 50, 200));"
        "    border-radius: 15px;"
        "    color: white;"
        "}"

        /* 歌曲信息卡片 */
        "QWidget#songInfoCard {"
        "    background: rgba(255, 255, 255, 30);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 50);"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 20px;"
        "    font-weight: bold;"
        "    color: #ffffff;"
        "    margin: 5px;"
        "}"
        "QLabel#artistLabel {"
        "    font-size: 14px;"
        "    color: rgba(255, 255, 255, 180);"
        "    margin: 2px;"
        "}"
        "QLabel#albumLabel {"
        "    font-size: 12px;"
        "    color: rgba(255, 255, 255, 150);"
        "    margin: 2px;"
        "}"

        /* 控制按钮卡片 */
        "QWidget#controlCard {"
        "    background: rgba(255, 255, 255, 20);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 30);"
        "}"
        /* 播放控制按钮 */
        "QPushButton#controlButton {"
        "    background: rgba(255, 255, 255, 100);"
        "    border: 2px solid rgba(255, 255, 255, 150);"
        "    border-radius: 25px;"
        "    color: white;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "}"
        "QPushButton#controlButton:hover {"
        "    background: rgba(255, 255, 255, 150);"
        "    border-color: rgba(255, 255, 255, 200);"
        "    transform: scale(1.1);"
        "}"
        "QPushButton#playButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 #4CAF50, stop:1 #45a049);"
        "    border: 3px solid rgba(255, 255, 255, 200);"
        "    border-radius: 30px;"
        "    color: white;"
        "    font-size: 20px;"
        "    font-weight: bold;"
        "}"
        "QPushButton#playButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 #5CBF60, stop:1 #4CAF50);"
        "    transform: scale(1.1);"
        "}"
        "QPushButton#modeButton {"
        "    background: rgba(156, 39, 176, 150);"
        "    border: 2px solid rgba(255, 255, 255, 100);"
        "    border-radius: 22px;"
        "    color: white;"
        "    font-size: 14px;"
        "}"
        "QPushButton#modeButton:hover {"
        "    background: rgba(156, 39, 176, 200);"
        "}"
        /* 进度条和音量控制卡片 */
        "QWidget#progressCard {"
        "    background: rgba(255, 255, 255, 20);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 30);"
        "    padding: 15px;"
        "}"
        "QLabel#timeLabel {"
        "    color: rgba(255, 255, 255, 200);"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"

        /* 现代化滑块样式 */
        "QSlider#seekSlider::groove:horizontal {"
        "    background: rgba(255, 255, 255, 100);"
        "    height: 6px;"
        "    border-radius: 3px;"
        "}"
        "QSlider#seekSlider::sub-page:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "                stop:0 #4CAF50, stop:1 #2196F3);"
        "    border-radius: 3px;"
        "}"
        "QSlider#seekSlider::handle:horizontal {"
        "    background: white;"
        "    border: 2px solid #4CAF50;"
        "    width: 16px;"
        "    height: 16px;"
        "    border-radius: 8px;"
        "    margin: -5px 0;"
        "}"
        "QSlider#volumeSlider::groove:horizontal {"
        "    background: rgba(255, 255, 255, 100);"
        "    height: 6px;"
        "    border-radius: 3px;"
        "}"
        "QSlider#volumeSlider::sub-page:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "                stop:0 #FF9800, stop:1 #FF5722);"
        "    border-radius: 3px;"
        "}"
        "QSlider#volumeSlider::handle:horizontal {"
        "    background: white;"
        "    border: 2px solid #FF9800;"
        "    width: 14px;"
        "    height: 14px;"
        "    border-radius: 7px;"
        "    margin: -4px 0;"
        "}"
        "QLabel#volumeLabel {"
        "    color: rgba(255, 255, 255, 200);"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton#volumeBtn {"
        "    background: rgba(255, 152, 0, 100);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 15px;"
        "    color: white;"
        "    font-size: 12px;"
        "}"
        "QPushButton#volumeBtn:hover {"
        "    background: rgba(255, 152, 0, 150);"
        "}"
        /* 播放列表卡片 */
        "QWidget#playlistCard {"
        "    background: rgba(255, 255, 255, 20);"
        "    border-radius: 12px;"
        "    border: 1px solid rgba(255, 255, 255, 30);"
        "    padding: 15px;"
        "}"
        "QLabel#playlistTitle {"
        "    color: white;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    margin-bottom: 10px;"
        "}"

        /* 功能按钮 */
        "QPushButton#actionButton {"
        "    background: rgba(100, 100, 100, 100);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 17px;"
        "    color: white;"
        "    font-size: 14px;"
        "}"
        "QPushButton#actionButton:hover {"
        "    background: rgba(150, 150, 150, 150);"
        "    transform: scale(1.1);"
        "}"
        "QPushButton#favoriteButton {"
        "    background: rgba(255, 193, 7, 150);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 17px;"
        "    color: white;"
        "    font-size: 14px;"
        "}"
        "QPushButton#favoriteButton:hover {"
        "    background: rgba(255, 193, 7, 200);"
        "    transform: scale(1.1);"
        "}"
        "QPushButton#themeButton {"
        "    background: rgba(156, 39, 176, 150);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 17px;"
        "    color: white;"
        "    font-size: 14px;"
        "}"
        "QPushButton#themeButton:hover {"
        "    background: rgba(156, 39, 176, 200);"
        "    transform: scale(1.1);"
        "}"
        "QPushButton#addThemeButton {"
        "    background: rgba(33, 150, 243, 150);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 17px;"
        "    color: white;"
        "    font-size: 14px;"
        "}"
        "QPushButton#addThemeButton:hover {"
        "    background: rgba(33, 150, 243, 200);"
        "    transform: scale(1.1);"
        "}"

        /* 现代化播放列表 */
        "QListWidget#modernPlaylist {"
        "    background: rgba(0, 0, 0, 50);"
        "    border: 1px solid rgba(255, 255, 255, 50);"
        "    border-radius: 8px;"
        "    color: white;"
        "    font-size: 13px;"
        "    outline: none;"
        "}"
        "QListWidget#modernPlaylist::item {"
        "    padding: 12px 15px;"
        "    border-bottom: 1px solid rgba(255, 255, 255, 20);"
        "    border-radius: 4px;"
        "    margin: 1px;"
        "}"
        "QListWidget#modernPlaylist::item:selected {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "                stop:0 rgba(76, 175, 80, 150), stop:1 rgba(33, 150, 243, 150));"
        "    color: white;"
        "    font-weight: bold;"
        "}"
        "QListWidget#modernPlaylist::item:hover {"
        "    background: rgba(255, 255, 255, 50);"
        "    color: white;"
        "}"
        "QListWidget#modernPlaylist::item:alternate {"
        "    background: rgba(255, 255, 255, 10);"
        "}"
    );
    
    // 初始化定时器
    updateTimer = new QTimer(this);
    connect(updateTimer, &QTimer::timeout, this, &MusicPlayerWidget::updatePlayTime);
    updateTimer->start(1000); // 每秒更新一次
}

MusicPlayerWidget::~MusicPlayerWidget()
{
    if (mediaPlayer) {
        mediaPlayer->stop();
    }
}

void MusicPlayerWidget::setupUI()
{
    // 创建主布局
    mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(15);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 顶部歌曲信息区域 - 现代化卡片式设计
    QWidget *songInfoCard = new QWidget(this);
    songInfoCard->setObjectName("songInfoCard");
    songInfoCard->setFixedHeight(120);

    QHBoxLayout *cardLayout = new QHBoxLayout(songInfoCard);
    cardLayout->setSpacing(20);

    // 专辑封面占位符
    QLabel *albumCover = new QLabel(this);
    albumCover->setFixedSize(80, 80);
    albumCover->setStyleSheet(
        "QLabel {"
        "    background-color: rgba(100, 100, 100, 100);"
        "    border-radius: 8px;"
        "    border: 2px solid rgba(255, 255, 255, 50);"
        "}"
    );
    albumCover->setAlignment(Qt::AlignCenter);
    albumCover->setText("♪");
    albumCover->setStyleSheet(albumCover->styleSheet() +
        "QLabel { font-size: 24px; color: white; }");

    // 歌曲信息文本区域
    QVBoxLayout *songInfoLayout = new QVBoxLayout();
    songInfoLayout->setSpacing(5);

    songTitleLabel = new QLabel("未选择歌曲", this);
    songTitleLabel->setObjectName("songTitleLabel");

    artistLabel = new QLabel("艺术家: 未知", this);
    artistLabel->setObjectName("artistLabel");

    albumLabel = new QLabel("专辑: 未知", this);
    albumLabel->setObjectName("albumLabel");

    songInfoLayout->addWidget(songTitleLabel);
    songInfoLayout->addWidget(artistLabel);
    songInfoLayout->addWidget(albumLabel);
    songInfoLayout->addStretch();

    cardLayout->addWidget(albumCover);
    cardLayout->addLayout(songInfoLayout);
    cardLayout->addStretch();

    mainLayout->addWidget(songInfoCard);
    
    // 中央播放控制区域 - 现代化圆形按钮设计
    QWidget *controlCard = new QWidget(this);
    controlCard->setObjectName("controlCard");
    controlCard->setFixedHeight(100);

    controlLayout = new QHBoxLayout(controlCard);
    controlLayout->setSpacing(20);

    // 创建圆形播放控制按钮
    previousButton = new QPushButton("⏮", this);
    previousButton->setObjectName("controlButton");
    previousButton->setFixedSize(50, 50);

    playPauseButton = new QPushButton("▶", this);
    playPauseButton->setObjectName("playButton");
    playPauseButton->setFixedSize(60, 60);

    nextButton = new QPushButton("⏭", this);
    nextButton->setObjectName("controlButton");
    nextButton->setFixedSize(50, 50);

    stopButton = new QPushButton("⏹", this);
    stopButton->setObjectName("controlButton");
    stopButton->setFixedSize(45, 45);

    playModeButton = new QPushButton("🔁", this);
    playModeButton->setObjectName("modeButton");
    playModeButton->setFixedSize(45, 45);

    controlLayout->addStretch();
    controlLayout->addWidget(playModeButton);
    controlLayout->addWidget(previousButton);
    controlLayout->addWidget(playPauseButton);
    controlLayout->addWidget(nextButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addStretch();

    mainLayout->addWidget(controlCard);
    
    // 进度控制区域 - 现代化进度条设计
    QWidget *progressCard = new QWidget(this);
    progressCard->setObjectName("progressCard");

    QVBoxLayout *progressLayout = new QVBoxLayout(progressCard);
    progressLayout->setSpacing(10);

    seekSlider = new QSlider(Qt::Horizontal, this);
    seekSlider->setRange(0, 0);
    seekSlider->setObjectName("seekSlider");
    seekSlider->setFixedHeight(6);

    seekLayout = new QHBoxLayout();
    currentTimeLabel = new QLabel("00:00", this);
    currentTimeLabel->setObjectName("timeLabel");
    totalTimeLabel = new QLabel("00:00", this);
    totalTimeLabel->setObjectName("timeLabel");

    seekLayout->addWidget(currentTimeLabel);
    seekLayout->addWidget(seekSlider);
    seekLayout->addWidget(totalTimeLabel);

    progressLayout->addLayout(seekLayout);

    // 音量控制区域 - 紧凑设计
    QHBoxLayout *volumeLayout = new QHBoxLayout();
    volumeLayout->setSpacing(15);

    QLabel *volumeIcon = new QLabel("🔊", this);
    volumeIcon->setFixedSize(20, 20);

    volumeSlider = new QSlider(Qt::Horizontal, this);
    volumeSlider->setRange(0, 100);
    volumeSlider->setValue(50);
    volumeSlider->setFixedWidth(120);
    volumeSlider->setFixedHeight(6);
    volumeSlider->setObjectName("volumeSlider");

    volumeLabel = new QLabel("50", this);
    volumeLabel->setObjectName("volumeLabel");
    volumeLabel->setFixedWidth(30);

    volumeUpButton = new QPushButton("🔊", this);
    volumeUpButton->setObjectName("volumeBtn");
    volumeUpButton->setFixedSize(30, 30);

    volumeDownButton = new QPushButton("🔉", this);
    volumeDownButton->setObjectName("volumeBtn");
    volumeDownButton->setFixedSize(30, 30);

    volumeLayout->addStretch();
    volumeLayout->addWidget(volumeDownButton);
    volumeLayout->addWidget(volumeSlider);
    volumeLayout->addWidget(volumeUpButton);
    volumeLayout->addWidget(volumeLabel);
    volumeLayout->addStretch();

    progressLayout->addLayout(volumeLayout);
    mainLayout->addWidget(progressCard);
    
    // 播放列表区域 - 现代化卡片设计
    QWidget *playlistCard = new QWidget(this);
    playlistCard->setObjectName("playlistCard");

    QVBoxLayout *playlistMainLayout = new QVBoxLayout(playlistCard);
    playlistMainLayout->setSpacing(15);

    // 播放列表标题和控制按钮
    QHBoxLayout *playlistHeaderLayout = new QHBoxLayout();
    QLabel *playlistTitle = new QLabel("播放列表", this);
    playlistTitle->setObjectName("playlistTitle");

    // 功能按钮组 - 现代化图标按钮
    addMusicButton = new QPushButton("➕", this);
    addMusicButton->setObjectName("actionButton");
    addMusicButton->setFixedSize(35, 35);
    addMusicButton->setToolTip("添加音乐");

    removeMusicButton = new QPushButton("🗑", this);
    removeMusicButton->setObjectName("actionButton");
    removeMusicButton->setFixedSize(35, 35);
    removeMusicButton->setToolTip("删除音乐");

    favoriteButton = new QPushButton("❤", this);
    favoriteButton->setObjectName("favoriteButton");
    favoriteButton->setFixedSize(35, 35);
    favoriteButton->setToolTip("我喜欢的");

    themeButton = new QPushButton("🎨", this);
    themeButton->setObjectName("themeButton");
    themeButton->setFixedSize(35, 35);
    themeButton->setToolTip("主题切换");

    addThemeButton = new QPushButton("🖼", this);
    addThemeButton->setObjectName("addThemeButton");
    addThemeButton->setFixedSize(35, 35);
    addThemeButton->setToolTip("添加主题图片");

    playlistHeaderLayout->addWidget(playlistTitle);
    playlistHeaderLayout->addStretch();
    playlistHeaderLayout->addWidget(addMusicButton);
    playlistHeaderLayout->addWidget(removeMusicButton);
    playlistHeaderLayout->addWidget(favoriteButton);
    playlistHeaderLayout->addWidget(themeButton);
    playlistHeaderLayout->addWidget(addThemeButton);

    // 播放列表 - 现代化列表设计
    playlistWidget = new QListWidget(this);
    playlistWidget->setObjectName("modernPlaylist");
    playlistWidget->setAlternatingRowColors(false);
    playlistWidget->setMaximumHeight(150); // 限制高度，大约显示5个项目
    playlistWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    playlistWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    playlistMainLayout->addLayout(playlistHeaderLayout);
    playlistMainLayout->addWidget(playlistWidget);
    
    // 完成主布局组装
    mainLayout->addWidget(playlistCard);
    mainLayout->addStretch();

    setLayout(mainLayout);
}

void MusicPlayerWidget::setupMediaPlayer()
{
    // 创建媒体播放器和播放列表
    mediaPlayer = new QMediaPlayer(this);
    playlist = new QMediaPlaylist(this);

    mediaPlayer->setPlaylist(playlist);
    mediaPlayer->setVolume(50);

    // 设置播放模式
    playlist->setPlaybackMode(QMediaPlaylist::Sequential);
}

void MusicPlayerWidget::setupConnections()
{
    // 播放控制连接
    connect(playPauseButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayPauseClicked);
    connect(previousButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPreviousClicked);
    connect(nextButton, &QPushButton::clicked, this, &MusicPlayerWidget::onNextClicked);
    connect(stopButton, &QPushButton::clicked, this, &MusicPlayerWidget::onStopClicked);
    connect(playModeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayModeClicked);

    // 音量控制连接
    connect(volumeSlider, &QSlider::valueChanged, this, &MusicPlayerWidget::onVolumeChanged);
    connect(volumeUpButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeUpClicked);
    connect(volumeDownButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeDownClicked);

    // 播放器状态连接
    connect(mediaPlayer, &QMediaPlayer::stateChanged, this, &MusicPlayerWidget::onStateChanged);
    connect(mediaPlayer, &QMediaPlayer::positionChanged, this, &MusicPlayerWidget::onPositionChanged);
    connect(mediaPlayer, &QMediaPlayer::durationChanged, this, &MusicPlayerWidget::onDurationChanged);
    connect(mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MusicPlayerWidget::onMediaStatusChanged);

    // 进度条控制连接
    connect(seekSlider, &QSlider::sliderPressed, this, &MusicPlayerWidget::onSeekSliderPressed);
    connect(seekSlider, &QSlider::sliderReleased, this, &MusicPlayerWidget::onSeekSliderReleased);
    connect(seekSlider, &QSlider::sliderMoved, this, &MusicPlayerWidget::onSeekSliderMoved);

    // 播放列表连接
    connect(addMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddMusicClicked);
    connect(removeMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onRemoveMusicClicked);
    connect(playlistWidget, &QListWidget::itemDoubleClicked, this, &MusicPlayerWidget::onPlaylistItemDoubleClicked);
    connect(favoriteButton, &QPushButton::clicked, this, &MusicPlayerWidget::onFavoriteClicked);
    connect(themeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onThemeClicked);
    connect(addThemeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddThemeImageClicked);

    // 播放列表变化连接
    connect(playlist, &QMediaPlaylist::currentIndexChanged, [this](int index) {
        if (index >= 0 && index < playlistWidget->count()) {
            playlistWidget->setCurrentRow(index);
            QListWidgetItem *item = playlistWidget->item(index);
            if (item) {
                QString filePath = item->data(Qt::UserRole).toString();
                QString songTitle = extractSongTitle(filePath);
                songTitleLabel->setText(songTitle);
                artistLabel->setText("艺术家: 未知");
                albumLabel->setText("专辑: 未知");

                // 切换歌曲时自动切换主题
                switchTheme();
            }
        }
    });
}

// 播放控制槽函数
void MusicPlayerWidget::onPlayPauseClicked()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        mediaPlayer->pause();
    } else {
        if (playlist->isEmpty()) {
            QMessageBox::information(this, "提示", "播放列表为空，请先添加音乐文件！");
            return;
        }
        mediaPlayer->play();
    }
}

void MusicPlayerWidget::onPreviousClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->previous();
    }
}

void MusicPlayerWidget::onNextClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->next();
    }
}

void MusicPlayerWidget::onStopClicked()
{
    mediaPlayer->stop();
    seekSlider->setValue(0);
    currentTimeLabel->setText("00:00");
}

// 音量控制槽函数
void MusicPlayerWidget::onVolumeChanged(int volume)
{
    mediaPlayer->setVolume(volume);
    volumeLabel->setText(QString("音量: %1").arg(volume));
}

void MusicPlayerWidget::onVolumeUpClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMin(100, currentVolume + 10);
    volumeSlider->setValue(newVolume);
}

void MusicPlayerWidget::onVolumeDownClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMax(0, currentVolume - 10);
    volumeSlider->setValue(newVolume);
}

// 播放模式控制
void MusicPlayerWidget::onPlayModeClicked()
{
    switch (currentPlayMode) {
    case Sequential:
        currentPlayMode = Loop;
        playlist->setPlaybackMode(QMediaPlaylist::Loop);
        break;
    case Loop:
        currentPlayMode = CurrentItemInLoop;
        playlist->setPlaybackMode(QMediaPlaylist::CurrentItemInLoop);
        break;
    case CurrentItemInLoop:
        currentPlayMode = Random;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential); // 随机模式用Sequential，在next/previous中处理
        break;
    case Random:
        currentPlayMode = Sequential;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential);
        break;
    }
    updatePlayModeButton();
}

// 播放器状态处理
void MusicPlayerWidget::onStateChanged(QMediaPlayer::State state)
{
    updatePlayPauseButton();
}

void MusicPlayerWidget::onPositionChanged(qint64 position)
{
    if (!isSeekSliderPressed) {
        seekSlider->setValue(position);
    }
    currentTimeLabel->setText(formatTime(position));
}

void MusicPlayerWidget::onDurationChanged(qint64 duration)
{
    seekSlider->setRange(0, duration);
    totalTimeLabel->setText(formatTime(duration));
}

void MusicPlayerWidget::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    if (status == QMediaPlayer::LoadedMedia) {
        // 媒体加载完成
    } else if (status == QMediaPlayer::InvalidMedia) {
        QMessageBox::warning(this, "错误", "无法播放该媒体文件！");
    }
}

// 进度条控制
void MusicPlayerWidget::onSeekSliderPressed()
{
    isSeekSliderPressed = true;
}

void MusicPlayerWidget::onSeekSliderReleased()
{
    isSeekSliderPressed = false;
    mediaPlayer->setPosition(seekSlider->value());
}

void MusicPlayerWidget::onSeekSliderMoved(int position)
{
    currentTimeLabel->setText(formatTime(position));
}

// 播放列表操作
void MusicPlayerWidget::onAddMusicClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(
        this,
        "选择音乐文件",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "音频文件 (*.mp3 *.wav *.flac *.aac *.ogg *.wma);;所有文件 (*.*)"
    );

    for (const QString &fileName : fileNames) {
        addMusicToPlaylist(fileName);
    }
}

void MusicPlayerWidget::onRemoveMusicClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        // 从播放列表中移除
        playlist->removeMedia(currentRow);

        // 从界面列表中移除
        QListWidgetItem *item = playlistWidget->takeItem(currentRow);
        delete item;

        QMessageBox::information(this, "成功", "已删除选中的音乐文件！");
    } else {
        QMessageBox::warning(this, "警告", "请先选择要删除的音乐文件！");
    }
}

void MusicPlayerWidget::onPlaylistItemDoubleClicked(QListWidgetItem *item)
{
    int row = playlistWidget->row(item);
    playlist->setCurrentIndex(row);
    mediaPlayer->play();
}

// 我喜欢的功能
void MusicPlayerWidget::onFavoriteClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        QListWidgetItem *item = playlistWidget->item(currentRow);
        QString filePath = item->data(Qt::UserRole).toString();

        if (favoriteList.contains(filePath)) {
            favoriteList.removeOne(filePath);
            QMessageBox::information(this, "取消收藏", "已从我喜欢的列表中移除！");
        } else {
            favoriteList.append(filePath);
            QMessageBox::information(this, "添加收藏", "已添加到我喜欢的列表！");
        }
        saveFavorites();
        updatePlaylistDisplay();
    } else {
        QMessageBox::warning(this, "警告", "请先选择要收藏的歌曲！");
    }
}

// 主题切换功能
void MusicPlayerWidget::onThemeClicked()
{
    switchTheme();
    QMessageBox::information(this, "主题切换", "主题已切换！");
}

// 添加主题图片
void MusicPlayerWidget::onAddThemeImageClicked()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择主题图片",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*.*)"
    );

    if (!fileName.isEmpty()) {
        themeImages.append(fileName);
        QMessageBox::information(this, "成功", "主题图片已添加！");
    }
}

// 定时器更新
void MusicPlayerWidget::updatePlayTime()
{
    // 这里可以添加额外的定时更新逻辑
    // 目前播放时间更新通过positionChanged信号处理
}

// 辅助方法
void MusicPlayerWidget::updatePlayModeButton()
{
    switch (currentPlayMode) {
    case Sequential:
        playModeButton->setText("🔁");
        playModeButton->setToolTip("顺序播放");
        break;
    case Loop:
        playModeButton->setText("🔄");
        playModeButton->setToolTip("列表循环");
        break;
    case CurrentItemInLoop:
        playModeButton->setText("🔂");
        playModeButton->setToolTip("单曲循环");
        break;
    case Random:
        playModeButton->setText("🔀");
        playModeButton->setToolTip("随机播放");
        break;
    }
}

void MusicPlayerWidget::updatePlayPauseButton()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        playPauseButton->setText("⏸");
    } else {
        playPauseButton->setText("▶");
    }
}

void MusicPlayerWidget::addMusicToPlaylist(const QString &filePath)
{
    // 添加到媒体播放列表
    playlist->addMedia(QUrl::fromLocalFile(filePath));

    // 添加到界面列表，显示正确的歌名
    QString songTitle = extractSongTitle(filePath);

    QListWidgetItem *item = new QListWidgetItem(songTitle);
    item->setData(Qt::UserRole, filePath); // 存储完整路径
    item->setToolTip(filePath); // 设置工具提示显示完整路径

    // 如果是收藏的歌曲，添加标记
    if (favoriteList.contains(filePath)) {
        item->setText("❤ " + songTitle);
    }

    playlistWidget->addItem(item);
}

QString MusicPlayerWidget::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}

// 主题切换实现
void MusicPlayerWidget::switchTheme()
{
    if (!themeImages.isEmpty()) {
        currentThemeIndex = (currentThemeIndex + 1) % themeImages.size();
        applyTheme(themeImages[currentThemeIndex]);
    }
}

// 加载主题图片
void MusicPlayerWidget::loadThemeImages()
{
    // 添加一些默认主题
    themeImages << ":/themes/default.jpg"
                << ":/themes/blue.jpg"
                << ":/themes/purple.jpg"
                << ":/themes/green.jpg";

    // 如果没有默认主题，创建纯色主题
    if (themeImages.isEmpty()) {
        themeImages << "default_blue" << "default_purple" << "default_green" << "default_orange";
    }
}

// 应用主题
void MusicPlayerWidget::applyTheme(const QString &imagePath)
{
    currentThemePath = imagePath;

    QString styleSheet;
    if (imagePath.startsWith("default_")) {
        // 纯色主题
        QString color = imagePath.split("_")[1];
        if (color == "blue") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(33, 150, 243, 180), stop:1 rgba(3, 169, 244, 180));"
                "    border-radius: 10px;"
                "}"
            );
        } else if (color == "purple") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(156, 39, 176, 180), stop:1 rgba(233, 30, 99, 180));"
                "    border-radius: 10px;"
                "}"
            );
        } else if (color == "green") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(76, 175, 80, 180), stop:1 rgba(139, 195, 74, 180));"
                "    border-radius: 10px;"
                "}"
            );
        } else if (color == "orange") {
            styleSheet = QString(
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(255, 152, 0, 180), stop:1 rgba(255, 193, 7, 180));"
                "    border-radius: 10px;"
                "}"
            );
        }
    } else {
        // 图片主题
        styleSheet = QString(
            "MusicPlayerWidget {"
            "    background-image: url(%1);"
            "    background-position: center;"
            "    background-repeat: no-repeat;"
            "    background-attachment: fixed;"
            "    border-radius: 10px;"
            "}"
            "MusicPlayerWidget::before {"
            "    content: '';"
            "    position: absolute;"
            "    top: 0; left: 0; right: 0; bottom: 0;"
            "    background-color: rgba(0, 0, 0, 0.25);"
            "    border-radius: 10px;"
            "}"
        ).arg(imagePath);
    }

    // 保持其他样式不变，只更新背景
    setStyleSheet(styleSheet +
        "QLabel {"
        "    color: #333;"
        "    font-size: 12px;"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    color: #2196F3;"
        "}"
        "QPushButton {"
        "    padding: 8px 16px;"
        "    border: 1px solid #ddd;"
        "    border-radius: 6px;"
        "    background-color: rgba(255, 255, 255, 190);"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgba(230, 243, 255, 220);"
        "    border-color: #2196F3;"
        "}"
        "QPushButton#favoriteButton {"
        "    background-color: rgba(255, 193, 7, 190);"
        "    color: white;"
        "}"
        "QPushButton#themeButton {"
        "    background-color: rgba(156, 39, 176, 190);"
        "    color: white;"
        "}"
        "QListWidget {"
        "    border: 1px solid #ddd;"
        "    background-color: rgba(255, 255, 255, 180);"
        "    border-radius: 5px;"
        "    max-height: 150px;"
        "}"
        "QListWidget::item {"
        "    padding: 8px;"
        "    border-bottom: 1px solid #eee;"
        "    border-radius: 3px;"
        "    margin: 2px;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: rgba(33, 150, 243, 200);"
        "    color: white;"
        "}"
    );
}

// 保存收藏列表
void MusicPlayerWidget::saveFavorites()
{
    QJsonArray jsonArray;
    for (const QString &filePath : favoriteList) {
        jsonArray.append(filePath);
    }

    QJsonObject jsonObject;
    jsonObject["favorites"] = jsonArray;

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/favorites.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载收藏列表
void MusicPlayerWidget::loadFavorites()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/favorites.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();
        QJsonArray jsonArray = jsonObject["favorites"].toArray();

        favoriteList.clear();
        for (const QJsonValue &value : jsonArray) {
            favoriteList.append(value.toString());
        }
    }
}

// 提取歌曲标题
QString MusicPlayerWidget::extractSongTitle(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();

    // 尝试从文件名中提取歌曲信息
    // 处理常见的命名格式：艺术家 - 歌名.mp3
    if (baseName.contains(" - ")) {
        QStringList parts = baseName.split(" - ");
        if (parts.size() >= 2) {
            return parts[1].trimmed(); // 返回歌名部分
        }
    }

    // 如果没有特殊格式，直接返回文件名
    return baseName;
}

// 更新播放列表显示
void MusicPlayerWidget::updatePlaylistDisplay()
{
    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        QString filePath = item->data(Qt::UserRole).toString();
        QString songTitle = extractSongTitle(filePath);

        if (favoriteList.contains(filePath)) {
            item->setText("❤ " + songTitle);
        } else {
            item->setText(songTitle);
        }
    }
}
