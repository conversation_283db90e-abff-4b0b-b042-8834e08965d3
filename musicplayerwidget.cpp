#include "musicplayerwidget.h"
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>

MusicPlayerWidget::MusicPlayerWidget(QWidget *parent)
    : QWidget(parent)
    , mediaPlayer(nullptr)
    , playlist(nullptr)
    , currentPlayMode(Sequential)
    , isSeekSliderPressed(false)
    , currentBackgroundIndex(0)
    , isPlayingFavorites(false)
{
    setupUI();
    setupMediaPlayer();
    setupConnections();

    // 设置窗口属性
    setWindowTitle("音乐播放器");
    resize(900, 700);

    // 设置16:9尺寸和网易云音乐风格
    setFixedSize(960, 540); // 16:9比例

    // 加载收藏列表、自定义歌曲名、专辑封面和背景图片
    loadFavorites();
    loadCustomSongNames();
    loadAlbumCovers();
    loadBackgroundImages();
    setDefaultBackground();

    // 强制重新应用优化后的按钮样式
    QString optimizedStyle = getNetEaseCloudMusicStyle();
    setStyleSheet(optimizedStyle);

    // 强制刷新所有按钮的样式
    playPauseButton->style()->unpolish(playPauseButton);
    playPauseButton->style()->polish(playPauseButton);
    previousButton->style()->unpolish(previousButton);
    previousButton->style()->polish(previousButton);
    nextButton->style()->unpolish(nextButton);
    nextButton->style()->polish(nextButton);
    stopButton->style()->unpolish(stopButton);
    stopButton->style()->polish(stopButton);
    playModeButton->style()->unpolish(playModeButton);
    playModeButton->style()->polish(playModeButton);
    volumeUpButton->style()->unpolish(volumeUpButton);
    volumeUpButton->style()->polish(volumeUpButton);
    volumeDownButton->style()->unpolish(volumeDownButton);
    volumeDownButton->style()->polish(volumeDownButton);
    addMusicButton->style()->unpolish(addMusicButton);
    addMusicButton->style()->polish(addMusicButton);
    removeMusicButton->style()->unpolish(removeMusicButton);
    removeMusicButton->style()->polish(removeMusicButton);
    favoriteButton->style()->unpolish(favoriteButton);
    favoriteButton->style()->polish(favoriteButton);
    playFavoritesButton->style()->unpolish(playFavoritesButton);
    playFavoritesButton->style()->polish(playFavoritesButton);
    

    

}

MusicPlayerWidget::~MusicPlayerWidget()
{
    if (mediaPlayer) {
        mediaPlayer->stop();
    }
}

// 事件过滤器 - 处理专辑封面点击
bool MusicPlayerWidget::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == albumCoverLabel && event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->button() == Qt::LeftButton) {
            onAlbumCoverClicked();
            return true;
        }
    }
    return QWidget::eventFilter(obj, event);
}

void MusicPlayerWidget::setupUI()
{
    // 创建主布局 - 网易云音乐16:9风格
    mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(8);
    mainLayout->setContentsMargins(12, 12, 12, 12);

    // 顶部歌曲信息区域 - 网易云音乐风格
    QWidget *songInfoCard = new QWidget(this);
    songInfoCard->setObjectName("songInfoCard");
    songInfoCard->setFixedHeight(100);

    QHBoxLayout *cardLayout = new QHBoxLayout(songInfoCard);
    cardLayout->setSpacing(20);

    // 专辑封面 - 可点击添加图片
    albumCoverLabel = new QLabel(this);
    albumCoverLabel->setFixedSize(70, 70);
    albumCoverLabel->setStyleSheet(
        "QLabel {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 rgba(31, 194, 124, 100), stop:1 rgba(49, 194, 124, 80));"
        "    border: 2px dashed rgba(31, 194, 124, 150);"
        "    border-radius: 8px;"
        "    color: rgba(31, 194, 124, 200);"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    text-align: center;"
        "}"
        "QLabel:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
        "                stop:0 rgba(31, 194, 124, 150), stop:1 rgba(49, 194, 124, 120));"
        "    border-color: rgba(31, 194, 124, 200);"
        "    cursor: pointer;"
        "}"
    );
    albumCoverLabel->setText("🖼\n点击\n添加");
    albumCoverLabel->setAlignment(Qt::AlignCenter);
    albumCoverLabel->setWordWrap(true);
    albumCoverLabel->setToolTip("点击添加专辑封面");

    // 安装事件过滤器使专辑封面可点击
    albumCoverLabel->installEventFilter(this);

    // 歌曲信息文本区域
    QVBoxLayout *songInfoLayout = new QVBoxLayout();
    songInfoLayout->setSpacing(5);

    songTitleLabel = new QLabel("未选择歌曲", this);
    songTitleLabel->setObjectName("songTitleLabel");

    artistLabel = new QLabel("艺术家: 未知", this);
    artistLabel->setObjectName("artistLabel");

    albumLabel = new QLabel("专辑: 未知", this);
    albumLabel->setObjectName("albumLabel");

    songInfoLayout->addWidget(songTitleLabel);
    songInfoLayout->addWidget(artistLabel);
    songInfoLayout->addWidget(albumLabel);
    songInfoLayout->addStretch();

    cardLayout->addWidget(albumCoverLabel);
    cardLayout->addLayout(songInfoLayout);
    cardLayout->addStretch();

    mainLayout->addWidget(songInfoCard);
    
    // 中央播放控制区域 - 现代化圆形按钮设计
    QWidget *controlCard = new QWidget(this);
    controlCard->setObjectName("controlCard");
    controlCard->setFixedHeight(100);

    controlLayout = new QHBoxLayout(controlCard);
    controlLayout->setSpacing(20);

    // 创建圆形播放控制按钮
    previousButton = new QPushButton("⏮", this);
    previousButton->setObjectName("controlButton");
    previousButton->setFixedSize(50, 50);

    playPauseButton = new QPushButton("▶", this);
    playPauseButton->setObjectName("playButton");
    playPauseButton->setFixedSize(60, 60);

    nextButton = new QPushButton("⏭", this);
    nextButton->setObjectName("controlButton");
    nextButton->setFixedSize(50, 50);

    stopButton = new QPushButton("⏹", this);
    stopButton->setObjectName("controlButton");
    stopButton->setFixedSize(45, 45);

    playModeButton = new QPushButton("🔁", this);
    playModeButton->setObjectName("modeButton");
    playModeButton->setFixedSize(45, 45);

    controlLayout->addStretch();
    controlLayout->addWidget(playModeButton);
    controlLayout->addWidget(previousButton);
    controlLayout->addWidget(playPauseButton);
    controlLayout->addWidget(nextButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addStretch();

    mainLayout->addWidget(controlCard);
    
    // 进度控制区域 - 现代化进度条设计
    QWidget *progressCard = new QWidget(this);
    progressCard->setObjectName("progressCard");

    QVBoxLayout *progressLayout = new QVBoxLayout(progressCard);
    progressLayout->setSpacing(10);

    seekSlider = new QSlider(Qt::Horizontal, this);
    seekSlider->setRange(0, 0);
    seekSlider->setObjectName("seekSlider");
    seekSlider->setFixedHeight(6);

    seekLayout = new QHBoxLayout();
    currentTimeLabel = new QLabel("00:00", this);
    currentTimeLabel->setObjectName("timeLabel");
    totalTimeLabel = new QLabel("00:00", this);
    totalTimeLabel->setObjectName("timeLabel");

    seekLayout->addWidget(currentTimeLabel);
    seekLayout->addWidget(seekSlider);
    seekLayout->addWidget(totalTimeLabel);

    progressLayout->addLayout(seekLayout);

    // 音量控制区域 - 紧凑设计
    QHBoxLayout *volumeLayout = new QHBoxLayout();
    volumeLayout->setSpacing(15);

    QLabel *volumeIcon = new QLabel("🔊", this);
    volumeIcon->setFixedSize(20, 20);

    volumeSlider = new QSlider(Qt::Horizontal, this);
    volumeSlider->setRange(0, 100);
    volumeSlider->setValue(50);
    volumeSlider->setFixedWidth(120);
    volumeSlider->setFixedHeight(6);
    volumeSlider->setObjectName("volumeSlider");

    volumeLabel = new QLabel("50", this);
    volumeLabel->setObjectName("volumeLabel");
    volumeLabel->setFixedWidth(30);

    volumeUpButton = new QPushButton("🔊", this);
    volumeUpButton->setObjectName("volumeBtn");
    volumeUpButton->setFixedSize(30, 30);

    volumeDownButton = new QPushButton("🔉", this);
    volumeDownButton->setObjectName("volumeBtn");
    volumeDownButton->setFixedSize(30, 30);

    volumeLayout->addStretch();
    volumeLayout->addWidget(volumeDownButton);
    volumeLayout->addWidget(volumeSlider);
    volumeLayout->addWidget(volumeUpButton);
    volumeLayout->addWidget(volumeLabel);
    volumeLayout->addStretch();

    progressLayout->addLayout(volumeLayout);
    mainLayout->addWidget(progressCard);
    
    // 播放列表区域 - 现代化卡片设计
    QWidget *playlistCard = new QWidget(this);
    playlistCard->setObjectName("playlistCard");

    QVBoxLayout *playlistMainLayout = new QVBoxLayout(playlistCard);
    playlistMainLayout->setSpacing(15);

    // 播放列表标题和控制按钮
    QHBoxLayout *playlistHeaderLayout = new QHBoxLayout();
    QLabel *playlistTitle = new QLabel("播放列表", this);
    playlistTitle->setObjectName("playlistTitle");

    // 功能按钮组 - 现代化图标按钮
    addMusicButton = new QPushButton("➕", this);
    addMusicButton->setObjectName("actionButton");
    addMusicButton->setFixedSize(35, 35);
    addMusicButton->setToolTip("添加音乐");

    removeMusicButton = new QPushButton("🗑", this);
    removeMusicButton->setObjectName("actionButton");
    removeMusicButton->setFixedSize(35, 35);
    removeMusicButton->setToolTip("删除音乐");

    favoriteButton = new QPushButton("❤", this);
    favoriteButton->setObjectName("favoriteButton");
    favoriteButton->setFixedSize(35, 35);
    favoriteButton->setToolTip("我喜欢的");

    playFavoritesButton = new QPushButton("🎵", this);
    playFavoritesButton->setObjectName("actionButton");
    playFavoritesButton->setFixedSize(35, 35);
    playFavoritesButton->setToolTip("播放我喜欢的");

    playlistHeaderLayout->addWidget(playlistTitle);
    playlistHeaderLayout->addStretch();
    playlistHeaderLayout->addWidget(addMusicButton);
    playlistHeaderLayout->addWidget(removeMusicButton);
    playlistHeaderLayout->addWidget(favoriteButton);
    playlistHeaderLayout->addWidget(playFavoritesButton);

    // 播放列表 - 现代化列表设计
    playlistWidget = new QListWidget(this);
    playlistWidget->setObjectName("modernPlaylist");
    playlistWidget->setAlternatingRowColors(false);
    playlistWidget->setMaximumHeight(150); // 限制高度，大约显示5个项目
    playlistWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    playlistWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    playlistWidget->setContextMenuPolicy(Qt::CustomContextMenu);

    playlistMainLayout->addLayout(playlistHeaderLayout);
    playlistMainLayout->addWidget(playlistWidget);
    
    // 完成主布局组装
    mainLayout->addWidget(playlistCard);
    mainLayout->addStretch();

    setLayout(mainLayout);
}

void MusicPlayerWidget::setupMediaPlayer()
{
    // 创建媒体播放器和播放列表
    mediaPlayer = new QMediaPlayer(this);
    playlist = new QMediaPlaylist(this);

    mediaPlayer->setPlaylist(playlist);
    mediaPlayer->setVolume(50);

    // 设置播放模式
    playlist->setPlaybackMode(QMediaPlaylist::Sequential);
}

void MusicPlayerWidget::setupConnections()
{
    // 播放控制连接
    connect(playPauseButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayPauseClicked);
    connect(previousButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPreviousClicked);
    connect(nextButton, &QPushButton::clicked, this, &MusicPlayerWidget::onNextClicked);
    connect(stopButton, &QPushButton::clicked, this, &MusicPlayerWidget::onStopClicked);
    connect(playModeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayModeClicked);

    // 音量控制连接
    connect(volumeSlider, &QSlider::valueChanged, this, &MusicPlayerWidget::onVolumeChanged);
    connect(volumeUpButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeUpClicked);
    connect(volumeDownButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeDownClicked);

    // 播放器状态连接
    connect(mediaPlayer, &QMediaPlayer::stateChanged, this, &MusicPlayerWidget::onStateChanged);
    connect(mediaPlayer, &QMediaPlayer::positionChanged, this, &MusicPlayerWidget::onPositionChanged);
    connect(mediaPlayer, &QMediaPlayer::durationChanged, this, &MusicPlayerWidget::onDurationChanged);
    connect(mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MusicPlayerWidget::onMediaStatusChanged);

    // 进度条控制连接
    connect(seekSlider, &QSlider::sliderPressed, this, &MusicPlayerWidget::onSeekSliderPressed);
    connect(seekSlider, &QSlider::sliderReleased, this, &MusicPlayerWidget::onSeekSliderReleased);
    connect(seekSlider, &QSlider::sliderMoved, this, &MusicPlayerWidget::onSeekSliderMoved);

    // 播放列表连接
    connect(addMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddMusicClicked);
    connect(removeMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onRemoveMusicClicked);
    connect(playlistWidget, &QListWidget::itemClicked, this, &MusicPlayerWidget::onPlaylistItemClicked);
    connect(playlistWidget, &QListWidget::itemDoubleClicked, this, &MusicPlayerWidget::onPlaylistItemDoubleClicked);
    connect(playlistWidget, &QListWidget::customContextMenuRequested, this, &MusicPlayerWidget::onPlaylistItemRightClicked);
    connect(favoriteButton, &QPushButton::clicked, this, &MusicPlayerWidget::onFavoriteClicked);
    connect(playFavoritesButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayFavoritesClicked);

    // 播放列表变化连接
    connect(playlist, &QMediaPlaylist::currentIndexChanged, [this](int index) {
        if (index >= 0 && index < playlistWidget->count()) {
            playlistWidget->setCurrentRow(index);
            QListWidgetItem *item = playlistWidget->item(index);
            if (item) {
                QString filePath = item->data(Qt::UserRole).toString();
                QString songTitle = getDisplayName(filePath);
                songTitleLabel->setText(songTitle);
                artistLabel->setText("艺术家: 未知");
                albumLabel->setText("专辑: 未知");

                // 更新专辑封面显示
                updateAlbumCover(filePath);

                // 更新播放列表高亮显示
                updatePlaylistHighlight();
            }
        }
    });
}

// 播放控制槽函数
void MusicPlayerWidget::onPlayPauseClicked()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        mediaPlayer->pause();
    } else {
        if (playlist->isEmpty()) {
            QMessageBox::information(this, "提示", "播放列表为空，请先添加音乐文件！");
            return;
        }
        mediaPlayer->play();
    }
}

void MusicPlayerWidget::onPreviousClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->previous();
    }
}

void MusicPlayerWidget::onNextClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->next();
    }
}

void MusicPlayerWidget::onStopClicked()
{
    mediaPlayer->stop();
    seekSlider->setValue(0);
    currentTimeLabel->setText("00:00");
}

// 音量控制槽函数
void MusicPlayerWidget::onVolumeChanged(int volume)
{
    mediaPlayer->setVolume(volume);
    volumeLabel->setText(QString("%1%").arg(volume));
}

void MusicPlayerWidget::onVolumeUpClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMin(100, currentVolume + 10);
    volumeSlider->setValue(newVolume);
}

void MusicPlayerWidget::onVolumeDownClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMax(0, currentVolume - 10);
    volumeSlider->setValue(newVolume);
}

// 播放模式控制
void MusicPlayerWidget::onPlayModeClicked()
{
    switch (currentPlayMode) {
    case Sequential:
        currentPlayMode = Loop;
        playlist->setPlaybackMode(QMediaPlaylist::Loop);
        break;
    case Loop:
        currentPlayMode = CurrentItemInLoop;
        playlist->setPlaybackMode(QMediaPlaylist::CurrentItemInLoop);
        break;
    case CurrentItemInLoop:
        currentPlayMode = Random;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential); // 随机模式用Sequential，在next/previous中处理
        break;
    case Random:
        currentPlayMode = Sequential;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential);
        break;
    }
    updatePlayModeButton();
}

// 播放器状态处理
void MusicPlayerWidget::onStateChanged(QMediaPlayer::State state)
{
    updatePlayPauseButton();
    // 更新播放列表显示，显示播放状态图标
    updatePlaylistDisplay();
}

void MusicPlayerWidget::onPositionChanged(qint64 position)
{
    if (!isSeekSliderPressed) {
        seekSlider->setValue(position);
    }
    currentTimeLabel->setText(formatTime(position));
}

void MusicPlayerWidget::onDurationChanged(qint64 duration)
{
    seekSlider->setRange(0, duration);
    totalTimeLabel->setText(formatTime(duration));
}

void MusicPlayerWidget::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    if (status == QMediaPlayer::LoadedMedia) {
        // 媒体加载完成
    } else if (status == QMediaPlayer::InvalidMedia) {
        QMessageBox::warning(this, "错误", "无法播放该媒体文件！");
    }
}

// 进度条控制
void MusicPlayerWidget::onSeekSliderPressed()
{
    isSeekSliderPressed = true;
}

void MusicPlayerWidget::onSeekSliderReleased()
{
    isSeekSliderPressed = false;
    mediaPlayer->setPosition(seekSlider->value());
}

void MusicPlayerWidget::onSeekSliderMoved(int position)
{
    currentTimeLabel->setText(formatTime(position));
}

// 播放列表操作
void MusicPlayerWidget::onAddMusicClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(
        this,
        "选择音乐文件",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "音频文件 (*.mp3 *.wav *.flac *.aac *.ogg *.wma);;所有文件 (*.*)"
    );

    for (const QString &fileName : fileNames) {
        addMusicToPlaylist(fileName);
    }
}

void MusicPlayerWidget::onRemoveMusicClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        // 从播放列表中移除
        playlist->removeMedia(currentRow);

        // 从界面列表中移除
        QListWidgetItem *item = playlistWidget->takeItem(currentRow);
        delete item;

        QMessageBox::information(this, "成功", "已删除选中的音乐文件！");
    } else {
        QMessageBox::warning(this, "警告", "请先选择要删除的音乐文件！");
    }
}

// 播放列表单击播放和高亮
void MusicPlayerWidget::onPlaylistItemClicked(QListWidgetItem *item)
{
    if (!item) return;

    int row = playlistWidget->row(item);

    // 切换到选中的歌曲
    playlist->setCurrentIndex(row);

    // 更新歌曲信息显示
    QString filePath = item->data(Qt::UserRole).toString();
    QString songTitle = getDisplayName(filePath);
    songTitleLabel->setText(songTitle);
    artistLabel->setText("艺术家: 未知");
    albumLabel->setText("专辑: 未知");

    // 更新专辑封面显示
    updateAlbumCover(filePath);

    // 开始播放
    mediaPlayer->play();
    updatePlayPauseButton();

    // 切换背景图片
    switchBackgroundImage();

    // 更新播放列表高亮显示
    updatePlaylistHighlight();
}

// 播放列表双击播放（保持兼容性）
void MusicPlayerWidget::onPlaylistItemDoubleClicked(QListWidgetItem *item)
{
    // 双击时执行与单击相同的逻辑
    onPlaylistItemClicked(item);
}

// 我喜欢的功能
void MusicPlayerWidget::onFavoriteClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        QListWidgetItem *item = playlistWidget->item(currentRow);
        QString filePath = item->data(Qt::UserRole).toString();

        if (favoriteList.contains(filePath)) {
            favoriteList.removeOne(filePath);
            QMessageBox::information(this, "取消收藏", "已从我喜欢的列表中移除！");
        } else {
            favoriteList.append(filePath);
            QMessageBox::information(this, "添加收藏", "已添加到我喜欢的列表！");
        }
        saveFavorites();
        updatePlaylistDisplay();
    } else {
        QMessageBox::warning(this, "警告", "请先选择要收藏的歌曲！");
    }
}

// 专辑封面点击处理
void MusicPlayerWidget::onAlbumCoverClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow < 0) {
        QMessageBox::information(this, "提示", "请先选择一首歌曲，然后为其添加专辑封面。");
        return;
    }

    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择专辑封面",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.webp);;所有文件 (*.*)"
    );

    if (!fileName.isEmpty()) {
        QListWidgetItem *item = playlistWidget->item(currentRow);
        QString filePath = item->data(Qt::UserRole).toString();

        // 保存专辑封面路径
        albumCovers[filePath] = fileName;
        saveAlbumCovers();

        // 更新当前显示的专辑封面
        updateAlbumCover(filePath);

        QMessageBox::information(this, "成功", "专辑封面已设置！");
    }
}





// 辅助方法
void MusicPlayerWidget::updatePlayModeButton()
{
    switch (currentPlayMode) {
    case Sequential:
        playModeButton->setText("🔁");
        playModeButton->setToolTip("顺序播放");
        break;
    case Loop:
        playModeButton->setText("🔄");
        playModeButton->setToolTip("列表循环");
        break;
    case CurrentItemInLoop:
        playModeButton->setText("🔂");
        playModeButton->setToolTip("单曲循环");
        break;
    case Random:
        playModeButton->setText("🔀");
        playModeButton->setToolTip("随机播放");
        break;
    }
}

void MusicPlayerWidget::updatePlayPauseButton()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        playPauseButton->setText("⏸");
    } else {
        playPauseButton->setText("▶");
    }
}

void MusicPlayerWidget::addMusicToPlaylist(const QString &filePath)
{
    // 添加到媒体播放列表
    playlist->addMedia(QUrl::fromLocalFile(filePath));

    // 添加到界面列表，显示正确的歌名
    QString songTitle = getDisplayName(filePath);

    QListWidgetItem *item = new QListWidgetItem(songTitle);
    item->setData(Qt::UserRole, filePath); // 存储完整路径
    item->setToolTip(filePath); // 设置工具提示显示完整路径

    // 如果是收藏的歌曲，添加标记
    if (favoriteList.contains(filePath)) {
        item->setText("❤ " + songTitle);
    }

    playlistWidget->addItem(item);
}

QString MusicPlayerWidget::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}



// 保存收藏列表
void MusicPlayerWidget::saveFavorites()
{
    QJsonArray jsonArray;
    for (const QString &filePath : favoriteList) {
        jsonArray.append(filePath);
    }

    QJsonObject jsonObject;
    jsonObject["favorites"] = jsonArray;

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/favorites.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载收藏列表
void MusicPlayerWidget::loadFavorites()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/favorites.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();
        QJsonArray jsonArray = jsonObject["favorites"].toArray();

        favoriteList.clear();
        for (const QJsonValue &value : jsonArray) {
            favoriteList.append(value.toString());
        }
    }
}

// 提取歌曲标题（优化版）
QString MusicPlayerWidget::extractSongTitle(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();

    // 移除常见的音质标识
    QStringList qualityTags = {"320K", "320k", "FLAC", "flac", "HQ", "hq", "HD", "hd", "MP3", "mp3"};
    for (const QString &tag : qualityTags) {
        baseName = baseName.remove(QRegExp("\\[" + tag + "\\]", Qt::CaseInsensitive));
        baseName = baseName.remove(QRegExp("\\(" + tag + "\\)", Qt::CaseInsensitive));
        baseName = baseName.remove(QRegExp("_" + tag + "_", Qt::CaseInsensitive));
        baseName = baseName.remove(QRegExp("-" + tag + "-", Qt::CaseInsensitive));
    }

    // 移除多余的空格和特殊字符
    baseName = baseName.replace(QRegExp("\\s+"), " ").trimmed();

    // 处理常见的命名格式
    // 格式1: 艺术家 - 歌名
    if (baseName.contains(" - ")) {
        QStringList parts = baseName.split(" - ");
        if (parts.size() >= 2) {
            return parts[1].trimmed();
        }
    }

    // 格式2: 歌名 (艺术家)
    if (baseName.contains("(") && baseName.contains(")")) {
        int startPos = baseName.indexOf("(");
        if (startPos > 0) {
            return baseName.left(startPos).trimmed();
        }
    }

    // 格式3: 数字. 歌名
    QRegExp numberPrefix("^\\d+\\.\\s*");
    if (baseName.contains(numberPrefix)) {
        baseName = baseName.remove(numberPrefix);
    }

    // 格式4: 歌名_艺术家 或 歌名-艺术家
    if (baseName.contains("_")) {
        QStringList parts = baseName.split("_");
        if (parts.size() >= 2) {
            return parts[0].trimmed();
        }
    }

    // 如果没有特殊格式，直接返回处理后的文件名
    return baseName.isEmpty() ? fileInfo.baseName() : baseName;
}

// 获取显示名称（优先使用自定义名称）
QString MusicPlayerWidget::getDisplayName(const QString &filePath)
{
    if (customSongNames.contains(filePath)) {
        return customSongNames[filePath];
    }
    return extractSongTitle(filePath);
}

// 更新播放列表显示 - 包含播放状态指示器
void MusicPlayerWidget::updatePlaylistDisplay()
{
    int currentPlayingIndex = playlist->currentIndex();

    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        QString filePath = item->data(Qt::UserRole).toString();
        QString songTitle = getDisplayName(filePath);
        QString displayText;

        // 添加播放状态指示器
        if (i == currentPlayingIndex && mediaPlayer->state() == QMediaPlayer::PlayingState) {
            displayText = "🎵 ";  // 正在播放图标
        } else if (i == currentPlayingIndex) {
            displayText = "⏸ ";   // 暂停图标
        } else {
            displayText = "";
        }

        // 添加收藏状态
        if (favoriteList.contains(filePath)) {
            displayText += "❤ " + songTitle;
        } else {
            displayText += songTitle;
        }

        item->setText(displayText);
    }
}

// 播放列表右键菜单
void MusicPlayerWidget::onPlaylistItemRightClicked(const QPoint &pos)
{
    QListWidgetItem *item = playlistWidget->itemAt(pos);
    if (!item) return;

    QMenu contextMenu(this);

    QAction *renameAction = contextMenu.addAction("🏷 重命名");
    QAction *favoriteAction = nullptr;

    QString filePath = item->data(Qt::UserRole).toString();
    if (favoriteList.contains(filePath)) {
        favoriteAction = contextMenu.addAction("💔 取消收藏");
    } else {
        favoriteAction = contextMenu.addAction("❤ 添加收藏");
    }

    contextMenu.addSeparator();
    QAction *removeAction = contextMenu.addAction("🗑 删除");

    // 设置QQ音乐风格菜单样式
    contextMenu.setStyleSheet(
        "QMenu {"
        "    background-color: white;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 8px;"
        "    padding: 8px 0px;"
        "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);"
        "}"
        "QMenu::item {"
        "    padding: 10px 20px;"
        "    border-radius: 0px;"
        "    color: #333333;"
        "    font-size: 13px;"
        "}"
        "QMenu::item:selected {"
        "    background-color: #f5f5f5;"
        "    color: #31c27c;"
        "}"
        "QMenu::separator {"
        "    height: 1px;"
        "    background-color: #f0f0f0;"
        "    margin: 4px 0px;"
        "}"
    );

    QAction *selectedAction = contextMenu.exec(playlistWidget->mapToGlobal(pos));

    if (selectedAction == renameAction) {
        playlistWidget->setCurrentItem(item);
        onRenameSongClicked();
    } else if (selectedAction == favoriteAction) {
        playlistWidget->setCurrentItem(item);
        onFavoriteClicked();
    } else if (selectedAction == removeAction) {
        playlistWidget->setCurrentItem(item);
        onRemoveMusicClicked();
    }
}

// 重命名歌曲
void MusicPlayerWidget::onRenameSongClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow < 0) {
        QMessageBox::warning(this, "警告", "请先选择要重命名的歌曲！");
        return;
    }

    QListWidgetItem *item = playlistWidget->item(currentRow);
    QString filePath = item->data(Qt::UserRole).toString();
    QString currentName = getDisplayName(filePath);

    bool ok;
    QString newName = QInputDialog::getText(this, "重命名歌曲",
                                          "请输入新的歌曲名称:",
                                          QLineEdit::Normal,
                                          currentName, &ok);

    if (ok && !newName.trimmed().isEmpty()) {
        newName = newName.trimmed();
        customSongNames[filePath] = newName;
        saveCustomSongNames();
        updatePlaylistDisplay();

        // 如果当前正在播放这首歌，更新显示
        if (playlist->currentIndex() == currentRow) {
            songTitleLabel->setText(newName);
        }

        QMessageBox::information(this, "成功", "歌曲重命名成功！");
    }
}









// 保存自定义歌曲名
void MusicPlayerWidget::saveCustomSongNames()
{
    QJsonObject jsonObject;
    for (auto it = customSongNames.begin(); it != customSongNames.end(); ++it) {
        jsonObject[it.key()] = it.value();
    }

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/custom_song_names.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载自定义歌曲名
void MusicPlayerWidget::loadCustomSongNames()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/custom_song_names.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();

        customSongNames.clear();
        for (auto it = jsonObject.begin(); it != jsonObject.end(); ++it) {
            customSongNames[it.key()] = it.value().toString();
        }
    }
}

// 网易云音乐风格样式（不包含主窗口背景）
QString MusicPlayerWidget::getNetEaseCloudMusicStyle()
{
    return QString(

        /* QQ音乐风格歌曲信息卡片 */
        "QWidget#songInfoCard {"
        "    background: rgba(255, 255, 255, 200);"
        "    border-radius: 10px;"
        "    border: 1px solid rgba(31, 194, 124, 80);"
        "    margin: 12px;"
        "    padding: 8px;"
        "    backdrop-filter: blur(12px);"
        "    box-shadow: 0 2px 10px rgba(0, 0, 0, 50);"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 20px;"
        "    font-weight: bold;"
        "    color: #1a1a1a;"
        "    margin: 6px 8px;"
        "    text-shadow: 0 1px 2px rgba(255, 255, 255, 100);"
        "}"
        "QLabel#artistLabel {"
        "    font-size: 14px;"
        "    color: #555555;"
        "    margin: 3px 8px;"
        "    font-weight: 500;"
        "}"
        "QLabel#albumLabel {"
        "    font-size: 13px;"
        "    color: #777777;"
        "    margin: 3px 8px;"
        "    font-style: italic;"
        "}"

        /* QQ音乐风格控制按钮卡片 */
        "QWidget#controlCard {"
        "    background: rgba(255, 255, 255, 200);"
        "    border-radius: 10px;"
        "    border: 1px solid rgba(31, 194, 124, 80);"
        "    margin: 12px;"
        "    padding: 6px;"
        "    backdrop-filter: blur(12px);"
        "    box-shadow: 0 2px 10px rgba(0, 0, 0, 50);"
        "}"

        /* 网易云音乐风格控制按钮 */
        "QPushButton#controlButton {"
        "    background: #ffffff;"
        "    border: 1px solid #d9d9d9;"
        "    border-radius: 4px;"
        "    color: #333333;"
        "    font-size: 14px;"
        "    font-weight: normal;"
        "    padding: 8px 12px;"
        "    min-width: 40px;"
        "    min-height: 32px;"
        "}"
        "QPushButton#controlButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #c6c6c6;"
        "    color: #333333;"
        "}"
        "QPushButton#controlButton:pressed {"
        "    background: #e6e6e6;"
        "    border-color: #b3b3b3;"
        "    color: #333333;"
        "}"
        /* 网易云音乐风格主播放按钮 */
        "QPushButton#playButton {"
        "    background: #c20c0c;"
        "    border: 1px solid #a40000;"
        "    border-radius: 4px;"
        "    color: #ffffff;"
        "    font-size: 16px;"
        "    font-weight: normal;"
        "    padding: 10px 16px;"
        "    min-width: 50px;"
        "    min-height: 36px;"
        "}"
        "QPushButton#playButton:hover {"
        "    background: #d40000;"
        "    border-color: #a40000;"
        "    color: #ffffff;"
        "}"
        "QPushButton#playButton:pressed {"
        "    background: #a40000;"
        "    border-color: #8b0000;"
        "    color: #ffffff;"
        "}"
        /* 网易云音乐风格模式按钮 */
        "QPushButton#modeButton {"
        "    background: #ffffff;"
        "    border: 1px solid #d9d9d9;"
        "    border-radius: 4px;"
        "    color: #666666;"
        "    font-size: 12px;"
        "    font-weight: normal;"
        "    padding: 6px 10px;"
        "    min-width: 35px;"
        "    min-height: 28px;"
        "}"
        "QPushButton#modeButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #c6c6c6;"
        "    color: #333333;"
        "}"
        "QPushButton#modeButton:pressed {"
        "    background: #e6e6e6;"
        "    border-color: #b3b3b3;"
        "    color: #333333;"
        "}"

        /* QQ音乐进度条和音量控制卡片 */
        "QWidget#progressCard {"
        "    background: rgba(255, 255, 255, 200);"
        "    border-radius: 10px;"
        "    border: 1px solid rgba(31, 194, 124, 80);"
        "    padding: 12px;"
        "    margin: 12px;"
        "    backdrop-filter: blur(12px);"
        "    box-shadow: 0 2px 10px rgba(0, 0, 0, 50);"
        "}"
        "QLabel#timeLabel {"
        "    color: #555555;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    text-shadow: 0 1px 1px rgba(255, 255, 255, 80);"
        "}"

        /* QQ音乐专业滑块样式 - 进度条 */
        "QSlider#seekSlider::groove:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #e8e8e8, stop:1 #d8d8d8);"
        "    height: 5px;"
        "    border-radius: 3px;"
        "    border: 1px solid #d0d0d0;"
        "}"
        "QSlider#seekSlider::sub-page:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #31c27c, stop:1 #28a068);"
        "    border-radius: 3px;"
        "    border: 1px solid #1f8a5a;"
        "}"
        "QSlider#seekSlider::handle:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #ffffff, stop:1 #f0f0f0);"
        "    border: 2px solid #31c27c;"
        "    width: 16px;"
        "    height: 16px;"
        "    border-radius: 8px;"
        "    margin: -6px 0;"
        "    box-shadow: 0 2px 4px rgba(0, 0, 0, 30);"
        "}"
        "QSlider#seekSlider::handle:horizontal:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #f8fff8, stop:1 #e8f5e8);"
        "    border-color: #2bb673;"
        "    box-shadow: 0 3px 6px rgba(31, 194, 124, 50);"
        "    transform: scale(1.1);"
        "}"
        /* QQ音乐专业滑块样式 - 音量控制 */
        "QSlider#volumeSlider::groove:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #e8e8e8, stop:1 #d8d8d8);"
        "    height: 4px;"
        "    border-radius: 2px;"
        "    border: 1px solid #d0d0d0;"
        "}"
        "QSlider#volumeSlider::sub-page:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #31c27c, stop:1 #28a068);"
        "    border-radius: 2px;"
        "    border: 1px solid #1f8a5a;"
        "}"
        "QSlider#volumeSlider::handle:horizontal {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #ffffff, stop:1 #f0f0f0);"
        "    border: 2px solid #31c27c;"
        "    width: 14px;"
        "    height: 14px;"
        "    border-radius: 7px;"
        "    margin: -5px 0;"
        "    box-shadow: 0 1px 3px rgba(0, 0, 0, 25);"
        "}"
        "QSlider#volumeSlider::handle:horizontal:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 #f8fff8, stop:1 #e8f5e8);"
        "    border-color: #2bb673;"
        "    box-shadow: 0 2px 5px rgba(31, 194, 124, 40);"
        "    transform: scale(1.1);"
        "}"
        "QLabel#volumeLabel {"
        "    color: #555555;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    text-shadow: 0 1px 1px rgba(255, 255, 255, 80);"
        "}"
        /* 网易云音乐风格音量按钮 */
        "QPushButton#volumeBtn {"
        "    background: #ffffff;"
        "    border: 1px solid #d9d9d9;"
        "    border-radius: 3px;"
        "    color: #666666;"
        "    font-size: 11px;"
        "    font-weight: normal;"
        "    padding: 4px 8px;"
        "    min-width: 24px;"
        "    min-height: 24px;"
        "}"
        "QPushButton#volumeBtn:hover {"
        "    background: #f5f5f5;"
        "    border-color: #c6c6c6;"
        "    color: #333333;"
        "}"
        "QPushButton#volumeBtn:pressed {"
        "    background: #e6e6e6;"
        "    border-color: #b3b3b3;"
        "    color: #333333;"
        "}"

        /* QQ音乐播放列表卡片 */
        "QWidget#playlistCard {"
        "    background: rgba(255, 255, 255, 200);"
        "    border-radius: 10px;"
        "    border: 1px solid rgba(31, 194, 124, 80);"
        "    padding: 12px;"
        "    margin: 12px;"
        "    backdrop-filter: blur(12px);"
        "    box-shadow: 0 2px 10px rgba(0, 0, 0, 50);"
        "}"
        /* QQ音乐播放列表标题 */
        "QLabel#playlistTitle {"
        "    color: #1a1a1a;"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "    margin-bottom: 8px;"
        "    text-shadow: 0 1px 2px rgba(255, 255, 255, 100);"
        "}"

        /* 网易云音乐风格功能按钮 */
        "QPushButton#actionButton {"
        "    background: #ffffff;"
        "    border: 1px solid #d9d9d9;"
        "    border-radius: 3px;"
        "    color: #666666;"
        "    font-size: 12px;"
        "    font-weight: normal;"
        "    padding: 6px 10px;"
        "    min-width: 30px;"
        "    min-height: 28px;"
        "}"
        "QPushButton#actionButton:hover {"
        "    background: #f5f5f5;"
        "    border-color: #c6c6c6;"
        "    color: #333333;"
        "}"
        "QPushButton#actionButton:pressed {"
        "    background: #e6e6e6;"
        "    border-color: #b3b3b3;"
        "    color: #333333;"
        "}"
        /* 网易云音乐风格收藏按钮 */
        "QPushButton#favoriteButton {"
        "    background: #ffffff;"
        "    border: 1px solid #d9d9d9;"
        "    border-radius: 3px;"
        "    color: #c20c0c;"
        "    font-size: 12px;"
        "    font-weight: normal;"
        "    padding: 6px 10px;"
        "    min-width: 30px;"
        "    min-height: 28px;"
        "}"
        "QPushButton#favoriteButton:hover {"
        "    background: #fff5f5;"
        "    border-color: #c20c0c;"
        "    color: #c20c0c;"
        "}"
        "QPushButton#favoriteButton:pressed {"
        "    background: #ffe6e6;"
        "    border-color: #a40000;"
        "    color: #a40000;"
        "}"

        /* QQ音乐风格播放列表 - 优化高亮显示 */
        "QListWidget#modernPlaylist {"
        "    background: rgba(255, 255, 255, 180);"
        "    border: 1px solid rgba(255, 255, 255, 100);"
        "    border-radius: 8px;"
        "    color: #333333;"
        "    font-size: 13px;"
        "    outline: none;"
        "    backdrop-filter: blur(8px);"
        "    selection-background-color: transparent;"
        "}"
        "QListWidget#modernPlaylist::item {"
        "    padding: 10px 15px;"
        "    border-bottom: 1px solid rgba(255, 255, 255, 30);"
        "    border-radius: 6px;"
        "    margin: 1px 3px;"
        "    background: transparent;"
        "}"
        "QListWidget#modernPlaylist::item:selected {"
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, "
        "                stop:0 rgba(31, 194, 124, 240), stop:0.5 rgba(49, 194, 124, 220), stop:1 rgba(31, 194, 124, 200));"
        "    color: white;"
        "    font-weight: bold;"
        "    font-size: 14px;"
        "    border: 2px solid rgba(31, 194, 124, 255);"
        "    border-radius: 8px;"
        "    box-shadow: 0 4px 12px rgba(31, 194, 124, 150);"
        "    text-shadow: 0 1px 2px rgba(0, 0, 0, 100);"
        "}"
        "QListWidget#modernPlaylist::item:hover:!selected {"
        "    background: rgba(255, 255, 255, 150);"
        "    color: #333333;"
        "    border-radius: 6px;"
        "    border: 1px solid rgba(31, 194, 124, 100);"
        "}"
        "QListWidget#modernPlaylist::item:alternate {"
        "    background: transparent;"
        "}"
    );
}

// 更新播放列表高亮显示 - QQ音乐风格
void MusicPlayerWidget::updatePlaylistHighlight()
{
    int currentIndex = playlist->currentIndex();

    // 清除所有项目的选中状态和自定义样式
    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        if (item) {
            item->setSelected(false);
            // 重置为默认样式
            item->setData(Qt::UserRole + 1, false); // 标记为非播放状态
        }
    }

    // 设置当前播放项目为选中状态
    if (currentIndex >= 0 && currentIndex < playlistWidget->count()) {
        QListWidgetItem *currentItem = playlistWidget->item(currentIndex);
        if (currentItem) {
            playlistWidget->setCurrentItem(currentItem);
            currentItem->setSelected(true);
            // 标记为正在播放状态
            currentItem->setData(Qt::UserRole + 1, true);

            // 确保当前播放项目可见
            playlistWidget->scrollToItem(currentItem, QAbstractItemView::EnsureVisible);
        }
    }

    // 更新播放项目的特殊样式
    updatePlayingItemStyle();
}

// 更新正在播放项目的样式 - QQ音乐风格强化
void MusicPlayerWidget::updatePlayingItemStyle()
{
    int currentIndex = playlist->currentIndex();

    // 为所有项目重置样式
    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        if (item) {
            if (i == currentIndex) {
                // 正在播放的项目 - 特殊样式
                QFont font = item->font();
                font.setBold(true);
                font.setPointSize(14); // 稍大的字体
                item->setFont(font);

                // 设置特殊的文字颜色（在CSS中会被覆盖，但作为备用）
                item->setForeground(QColor(31, 194, 124));
            } else {
                // 普通项目 - 默认样式
                QFont font = item->font();
                font.setBold(false);
                font.setPointSize(13); // 默认字体大小
                item->setFont(font);

                // 默认文字颜色
                item->setForeground(QColor(51, 51, 51));
            }
        }
    }
}



// 切换背景图片
void MusicPlayerWidget::switchBackgroundImage()
{
    if (!backgroundImages.isEmpty()) {
        currentBackgroundIndex = (currentBackgroundIndex + 1) % backgroundImages.size();
        applyBackgroundImage(backgroundImages[currentBackgroundIndex]);
    }
}

// 加载背景图片列表
void MusicPlayerWidget::loadBackgroundImages()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/background_images.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonArray jsonArray = doc.array();

        backgroundImages.clear();
        for (const QJsonValue &value : jsonArray) {
            QString imagePath = value.toString();
            // 检查文件是否存在
            if (QFile::exists(imagePath)) {
                backgroundImages.append(imagePath);
            }
        }
    }

    // 如果没有背景图片，添加一些默认的渐变背景
    if (backgroundImages.isEmpty()) {
        backgroundImages << "default_gradient_1"
                        << "default_gradient_2"
                        << "default_gradient_3"
                        << "default_gradient_4";
    }
}

// 应用背景图片
void MusicPlayerWidget::applyBackgroundImage(const QString &imagePath)
{
    currentBackgroundPath = imagePath;

    QString backgroundStyle;

    if (imagePath.startsWith("default_gradient_")) {
        // 默认渐变背景
        if (imagePath == "default_gradient_1") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(135, 206, 250, 200), stop:1 rgba(176, 224, 230, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        } else if (imagePath == "default_gradient_2") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(255, 182, 193, 200), stop:1 rgba(255, 218, 185, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        } else if (imagePath == "default_gradient_3") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(221, 160, 221, 200), stop:1 rgba(238, 130, 238, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        } else if (imagePath == "default_gradient_4") {
            backgroundStyle =
                "MusicPlayerWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
                "                stop:0 rgba(152, 251, 152, 200), stop:1 rgba(144, 238, 144, 200));"
                "    border-radius: 8px;"
                "    color: #333333;"
                "}";
        }
    } else {
        // 图片背景
        QString normalizedPath = QDir::toNativeSeparators(imagePath);
        backgroundStyle = QString(
            "MusicPlayerWidget {"
            "    background-image: url(%1);"
            "    background-position: center;"
            "    background-repeat: no-repeat;"
            "    background-size: cover;"
            "    border-radius: 8px;"
            "    color: #333333;"
            "}"
        ).arg(normalizedPath);
    }

    // 应用背景样式，同时保持组件样式
    QString fullStyle = backgroundStyle + getNetEaseCloudMusicStyle();
    setStyleSheet(fullStyle);
}

// 设置默认背景
void MusicPlayerWidget::setDefaultBackground()
{
    if (!backgroundImages.isEmpty()) {
        applyBackgroundImage(backgroundImages[0]);
    } else {
        // 如果没有背景图片，使用默认的网易云音乐风格
        QString defaultStyle =
            "MusicPlayerWidget {"
            "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
            "                stop:0 #f5f5f5, stop:1 #ffffff);"
            "    border-radius: 8px;"
            "    color: #333333;"
            "}" + getNetEaseCloudMusicStyle();
        setStyleSheet(defaultStyle);
    }
}

// 保存背景图片列表
void MusicPlayerWidget::saveBackgroundImages()
{
    QJsonArray jsonArray;
    for (const QString &imagePath : backgroundImages) {
        // 只保存真实的图片文件路径，不保存默认渐变
        if (!imagePath.startsWith("default_gradient_")) {
            jsonArray.append(imagePath);
        }
    }

    QJsonDocument doc(jsonArray);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/background_images.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 保存专辑封面数据
void MusicPlayerWidget::saveAlbumCovers()
{
    QJsonObject jsonObject;
    for (auto it = albumCovers.begin(); it != albumCovers.end(); ++it) {
        jsonObject[it.key()] = it.value();
    }

    QJsonDocument doc(jsonObject);

    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(configPath);

    QFile file(configPath + "/album_covers.json");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        file.close();
    }
}

// 加载专辑封面数据
void MusicPlayerWidget::loadAlbumCovers()
{
    QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QFile file(configPath + "/album_covers.json");

    if (file.open(QIODevice::ReadOnly)) {
        QByteArray data = file.readAll();
        file.close();

        QJsonDocument doc = QJsonDocument::fromJson(data);
        QJsonObject jsonObject = doc.object();

        albumCovers.clear();
        for (auto it = jsonObject.begin(); it != jsonObject.end(); ++it) {
            QString filePath = it.key();
            QString coverPath = it.value().toString();
            // 检查封面文件是否存在
            if (QFile::exists(coverPath)) {
                albumCovers[filePath] = coverPath;
            }
        }
    }
}

// 更新专辑封面显示
void MusicPlayerWidget::updateAlbumCover(const QString &filePath)
{
    if (albumCovers.contains(filePath)) {
        QString coverPath = albumCovers[filePath];
        QPixmap pixmap(coverPath);
        if (!pixmap.isNull()) {
            // 缩放图片以适应标签大小
            QPixmap scaledPixmap = pixmap.scaled(70, 70, Qt::KeepAspectRatio, Qt::SmoothTransformation);
            albumCoverLabel->setPixmap(scaledPixmap);
            albumCoverLabel->setText("");
            albumCoverLabel->setStyleSheet(
                "QLabel {"
                "    border: 2px solid rgba(31, 194, 124, 150);"
                "    border-radius: 8px;"
                "}"
                "QLabel:hover {"
                "    border-color: rgba(31, 194, 124, 200);"
                "    cursor: pointer;"
                "}"
            );
            albumCoverLabel->setToolTip("点击更换专辑封面");
        }
    } else {
        // 恢复默认显示
        albumCoverLabel->setPixmap(QPixmap());
        albumCoverLabel->setText("🖼\n点击\n添加");
        albumCoverLabel->setStyleSheet(
            "QLabel {"
            "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
            "                stop:0 rgba(31, 194, 124, 100), stop:1 rgba(49, 194, 124, 80));"
            "    border: 2px dashed rgba(31, 194, 124, 150);"
            "    border-radius: 8px;"
            "    color: rgba(31, 194, 124, 200);"
            "    font-size: 16px;"
            "    font-weight: bold;"
            "    text-align: center;"
            "}"
            "QLabel:hover {"
            "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, "
            "                stop:0 rgba(31, 194, 124, 150), stop:1 rgba(49, 194, 124, 120));"
            "    border-color: rgba(31, 194, 124, 200);"
            "    cursor: pointer;"
            "}"
        );
        albumCoverLabel->setToolTip("点击添加专辑封面");
    }
}

// 播放我喜欢的歌曲
void MusicPlayerWidget::onPlayFavoritesClicked()
{
    if (favoriteList.isEmpty()) {
        QMessageBox::information(this, "提示", "您还没有收藏任何歌曲！\n请先添加一些歌曲到\"我喜欢的\"列表。");
        return;
    }

    if (isPlayingFavorites) {
        // 如果正在播放收藏列表，恢复原始播放列表
        restoreOriginalPlaylist();
        playFavoritesButton->setText("🎵");
        playFavoritesButton->setToolTip("播放我喜欢的");
        QMessageBox::information(this, "提示", "已恢复完整播放列表");
    } else {
        // 创建收藏歌曲播放列表
        createFavoritesPlaylist();
        playFavoritesButton->setText("📋");
        playFavoritesButton->setToolTip("恢复完整列表");
        QMessageBox::information(this, "提示", QString("正在播放我喜欢的歌曲（共%1首）").arg(favoriteList.size()));
    }
}

// 创建收藏歌曲播放列表
void MusicPlayerWidget::createFavoritesPlaylist()
{
    // 备份当前播放列表
    originalPlaylist.clear();
    for (int i = 0; i < playlistWidget->count(); ++i) {
        QListWidgetItem *item = playlistWidget->item(i);
        originalPlaylist.append(item->data(Qt::UserRole).toString());
    }

    // 清空当前播放列表
    playlist->clear();
    playlistWidget->clear();

    // 添加收藏的歌曲
    for (const QString &filePath : favoriteList) {
        // 检查文件是否存在
        if (QFile::exists(filePath)) {
            playlist->addMedia(QUrl::fromLocalFile(filePath));

            QString songTitle = getDisplayName(filePath);
            QListWidgetItem *item = new QListWidgetItem("❤ " + songTitle);
            item->setData(Qt::UserRole, filePath);
            item->setToolTip(filePath);
            playlistWidget->addItem(item);
        }
    }

    isPlayingFavorites = true;

    // 如果有歌曲，开始播放第一首
    if (playlist->mediaCount() > 0) {
        playlist->setCurrentIndex(0);
        mediaPlayer->play();
        updatePlaylistHighlight();
    }
}

// 恢复原始播放列表
void MusicPlayerWidget::restoreOriginalPlaylist()
{
    // 清空当前播放列表
    playlist->clear();
    playlistWidget->clear();

    // 恢复原始播放列表
    for (const QString &filePath : originalPlaylist) {
        if (QFile::exists(filePath)) {
            playlist->addMedia(QUrl::fromLocalFile(filePath));

            QString songTitle = getDisplayName(filePath);
            QListWidgetItem *item = new QListWidgetItem(songTitle);
            item->setData(Qt::UserRole, filePath);
            item->setToolTip(filePath);

            // 如果是收藏的歌曲，添加标记
            if (favoriteList.contains(filePath)) {
                item->setText("❤ " + songTitle);
            }

            playlistWidget->addItem(item);
        }
    }

    isPlayingFavorites = false;
    originalPlaylist.clear();

    // 更新播放列表显示
    updatePlaylistDisplay();
    updatePlaylistHighlight();
}
