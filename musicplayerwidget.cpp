#include "musicplayerwidget.h"
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>

MusicPlayerWidget::MusicPlayerWidget(QWidget *parent)
    : QWidget(parent)
    , mediaPlayer(nullptr)
    , playlist(nullptr)
    , currentPlayMode(Sequential)
    , isSeekSliderPressed(false)
{
    setupUI();
    setupMediaPlayer();
    setupConnections();
    
    // 设置窗口属性
    setWindowTitle("音乐播放器");
    resize(800, 600);
    
    // 设置样式
    setStyleSheet(
        "MusicPlayerWidget {"
        "    background-color: #f0f0f0;"
        "}"
        "QLabel {"
        "    color: #333;"
        "    font-size: 12px;"
        "}"
        "QLabel#songTitleLabel {"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "    color: #2196F3;"
        "}"
        "QPushButton {"
        "    padding: 8px 16px;"
        "    border: 1px solid #ddd;"
        "    border-radius: 4px;"
        "    background-color: white;"
        "    font-size: 12px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e6f3ff;"
        "    border-color: #2196F3;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #cce7ff;"
        "}"
        "QSlider::groove:horizontal {"
        "    border: 1px solid #bbb;"
        "    background: white;"
        "    height: 10px;"
        "    border-radius: 4px;"
        "}"
        "QSlider::sub-page:horizontal {"
        "    background: #2196F3;"
        "    border: 1px solid #777;"
        "    height: 10px;"
        "    border-radius: 4px;"
        "}"
        "QSlider::add-page:horizontal {"
        "    background: #fff;"
        "    border: 1px solid #777;"
        "    height: 10px;"
        "    border-radius: 4px;"
        "}"
        "QSlider::handle:horizontal {"
        "    background: #2196F3;"
        "    border: 1px solid #5c5c5c;"
        "    width: 18px;"
        "    margin: -2px 0;"
        "    border-radius: 3px;"
        "}"
        "QListWidget {"
        "    border: 1px solid #ddd;"
        "    background-color: white;"
        "    alternate-background-color: #f9f9f9;"
        "}"
        "QListWidget::item {"
        "    padding: 5px;"
        "    border-bottom: 1px solid #eee;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #2196F3;"
        "    color: white;"
        "}"
    );
    
    // 初始化定时器
    updateTimer = new QTimer(this);
    connect(updateTimer, &QTimer::timeout, this, &MusicPlayerWidget::updatePlayTime);
    updateTimer->start(1000); // 每秒更新一次
}

MusicPlayerWidget::~MusicPlayerWidget()
{
    if (mediaPlayer) {
        mediaPlayer->stop();
    }
}

void MusicPlayerWidget::setupUI()
{
    // 创建主布局
    mainLayout = new QVBoxLayout(this);
    
    // 歌曲信息区域
    songTitleLabel = new QLabel("未选择歌曲", this);
    songTitleLabel->setObjectName("songTitleLabel");
    songTitleLabel->setAlignment(Qt::AlignCenter);
    
    artistLabel = new QLabel("艺术家: 未知", this);
    artistLabel->setAlignment(Qt::AlignCenter);
    
    albumLabel = new QLabel("专辑: 未知", this);
    albumLabel->setAlignment(Qt::AlignCenter);
    
    infoLayout = new QHBoxLayout();
    QVBoxLayout *songInfoLayout = new QVBoxLayout();
    songInfoLayout->addWidget(songTitleLabel);
    songInfoLayout->addWidget(artistLabel);
    songInfoLayout->addWidget(albumLabel);
    infoLayout->addLayout(songInfoLayout);
    
    // 播放控制区域
    previousButton = new QPushButton("上一曲", this);
    playPauseButton = new QPushButton("播放", this);
    nextButton = new QPushButton("下一曲", this);
    stopButton = new QPushButton("停止", this);
    playModeButton = new QPushButton("顺序播放", this);
    
    controlLayout = new QHBoxLayout();
    controlLayout->addStretch();
    controlLayout->addWidget(previousButton);
    controlLayout->addWidget(playPauseButton);
    controlLayout->addWidget(nextButton);
    controlLayout->addWidget(stopButton);
    controlLayout->addWidget(playModeButton);
    controlLayout->addStretch();
    
    // 进度控制区域
    seekSlider = new QSlider(Qt::Horizontal, this);
    seekSlider->setRange(0, 0);
    currentTimeLabel = new QLabel("00:00", this);
    totalTimeLabel = new QLabel("00:00", this);
    
    seekLayout = new QHBoxLayout();
    seekLayout->addWidget(currentTimeLabel);
    seekLayout->addWidget(seekSlider);
    seekLayout->addWidget(totalTimeLabel);
    
    // 音量控制区域
    volumeSlider = new QSlider(Qt::Horizontal, this);
    volumeSlider->setRange(0, 100);
    volumeSlider->setValue(50);
    volumeSlider->setMaximumWidth(150);
    
    volumeLabel = new QLabel("音量: 50", this);
    volumeUpButton = new QPushButton("音量+", this);
    volumeDownButton = new QPushButton("音量-", this);
    
    volumeLayout = new QHBoxLayout();
    volumeLayout->addStretch();
    volumeLayout->addWidget(new QLabel("音量控制:", this));
    volumeLayout->addWidget(volumeDownButton);
    volumeLayout->addWidget(volumeSlider);
    volumeLayout->addWidget(volumeUpButton);
    volumeLayout->addWidget(volumeLabel);
    volumeLayout->addStretch();
    
    // 播放列表区域
    playlistWidget = new QListWidget(this);
    playlistWidget->setAlternatingRowColors(true);
    
    addMusicButton = new QPushButton("添加音乐", this);
    removeMusicButton = new QPushButton("删除音乐", this);
    
    playlistControlLayout = new QHBoxLayout();
    playlistControlLayout->addWidget(addMusicButton);
    playlistControlLayout->addWidget(removeMusicButton);
    playlistControlLayout->addStretch();
    
    // 组装主布局
    mainLayout->addLayout(infoLayout);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(controlLayout);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(seekLayout);
    mainLayout->addSpacing(10);
    mainLayout->addLayout(volumeLayout);
    mainLayout->addSpacing(20);
    mainLayout->addWidget(new QLabel("播放列表:", this));
    mainLayout->addLayout(playlistControlLayout);
    mainLayout->addWidget(playlistWidget);
    
    setLayout(mainLayout);
}

void MusicPlayerWidget::setupMediaPlayer()
{
    // 创建媒体播放器和播放列表
    mediaPlayer = new QMediaPlayer(this);
    playlist = new QMediaPlaylist(this);

    mediaPlayer->setPlaylist(playlist);
    mediaPlayer->setVolume(50);

    // 设置播放模式
    playlist->setPlaybackMode(QMediaPlaylist::Sequential);
}

void MusicPlayerWidget::setupConnections()
{
    // 播放控制连接
    connect(playPauseButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayPauseClicked);
    connect(previousButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPreviousClicked);
    connect(nextButton, &QPushButton::clicked, this, &MusicPlayerWidget::onNextClicked);
    connect(stopButton, &QPushButton::clicked, this, &MusicPlayerWidget::onStopClicked);
    connect(playModeButton, &QPushButton::clicked, this, &MusicPlayerWidget::onPlayModeClicked);

    // 音量控制连接
    connect(volumeSlider, &QSlider::valueChanged, this, &MusicPlayerWidget::onVolumeChanged);
    connect(volumeUpButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeUpClicked);
    connect(volumeDownButton, &QPushButton::clicked, this, &MusicPlayerWidget::onVolumeDownClicked);

    // 播放器状态连接
    connect(mediaPlayer, &QMediaPlayer::stateChanged, this, &MusicPlayerWidget::onStateChanged);
    connect(mediaPlayer, &QMediaPlayer::positionChanged, this, &MusicPlayerWidget::onPositionChanged);
    connect(mediaPlayer, &QMediaPlayer::durationChanged, this, &MusicPlayerWidget::onDurationChanged);
    connect(mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MusicPlayerWidget::onMediaStatusChanged);

    // 进度条控制连接
    connect(seekSlider, &QSlider::sliderPressed, this, &MusicPlayerWidget::onSeekSliderPressed);
    connect(seekSlider, &QSlider::sliderReleased, this, &MusicPlayerWidget::onSeekSliderReleased);
    connect(seekSlider, &QSlider::sliderMoved, this, &MusicPlayerWidget::onSeekSliderMoved);

    // 播放列表连接
    connect(addMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onAddMusicClicked);
    connect(removeMusicButton, &QPushButton::clicked, this, &MusicPlayerWidget::onRemoveMusicClicked);
    connect(playlistWidget, &QListWidget::itemDoubleClicked, this, &MusicPlayerWidget::onPlaylistItemDoubleClicked);

    // 播放列表变化连接
    connect(playlist, &QMediaPlaylist::currentIndexChanged, [this](int index) {
        if (index >= 0 && index < playlistWidget->count()) {
            playlistWidget->setCurrentRow(index);
            QListWidgetItem *item = playlistWidget->item(index);
            if (item) {
                QString fileName = QFileInfo(item->data(Qt::UserRole).toString()).baseName();
                songTitleLabel->setText(fileName);
                artistLabel->setText("艺术家: 未知");
                albumLabel->setText("专辑: 未知");
            }
        }
    });
}

// 播放控制槽函数
void MusicPlayerWidget::onPlayPauseClicked()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        mediaPlayer->pause();
    } else {
        if (playlist->isEmpty()) {
            QMessageBox::information(this, "提示", "播放列表为空，请先添加音乐文件！");
            return;
        }
        mediaPlayer->play();
    }
}

void MusicPlayerWidget::onPreviousClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->previous();
    }
}

void MusicPlayerWidget::onNextClicked()
{
    if (currentPlayMode == Random) {
        // 随机模式下随机选择
        int randomIndex = qrand() % playlist->mediaCount();
        playlist->setCurrentIndex(randomIndex);
    } else {
        playlist->next();
    }
}

void MusicPlayerWidget::onStopClicked()
{
    mediaPlayer->stop();
    seekSlider->setValue(0);
    currentTimeLabel->setText("00:00");
}

// 音量控制槽函数
void MusicPlayerWidget::onVolumeChanged(int volume)
{
    mediaPlayer->setVolume(volume);
    volumeLabel->setText(QString("音量: %1").arg(volume));
}

void MusicPlayerWidget::onVolumeUpClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMin(100, currentVolume + 10);
    volumeSlider->setValue(newVolume);
}

void MusicPlayerWidget::onVolumeDownClicked()
{
    int currentVolume = volumeSlider->value();
    int newVolume = qMax(0, currentVolume - 10);
    volumeSlider->setValue(newVolume);
}

// 播放模式控制
void MusicPlayerWidget::onPlayModeClicked()
{
    switch (currentPlayMode) {
    case Sequential:
        currentPlayMode = Loop;
        playlist->setPlaybackMode(QMediaPlaylist::Loop);
        break;
    case Loop:
        currentPlayMode = CurrentItemInLoop;
        playlist->setPlaybackMode(QMediaPlaylist::CurrentItemInLoop);
        break;
    case CurrentItemInLoop:
        currentPlayMode = Random;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential); // 随机模式用Sequential，在next/previous中处理
        break;
    case Random:
        currentPlayMode = Sequential;
        playlist->setPlaybackMode(QMediaPlaylist::Sequential);
        break;
    }
    updatePlayModeButton();
}

// 播放器状态处理
void MusicPlayerWidget::onStateChanged(QMediaPlayer::State state)
{
    updatePlayPauseButton();
}

void MusicPlayerWidget::onPositionChanged(qint64 position)
{
    if (!isSeekSliderPressed) {
        seekSlider->setValue(position);
    }
    currentTimeLabel->setText(formatTime(position));
}

void MusicPlayerWidget::onDurationChanged(qint64 duration)
{
    seekSlider->setRange(0, duration);
    totalTimeLabel->setText(formatTime(duration));
}

void MusicPlayerWidget::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    if (status == QMediaPlayer::LoadedMedia) {
        // 媒体加载完成
    } else if (status == QMediaPlayer::InvalidMedia) {
        QMessageBox::warning(this, "错误", "无法播放该媒体文件！");
    }
}

// 进度条控制
void MusicPlayerWidget::onSeekSliderPressed()
{
    isSeekSliderPressed = true;
}

void MusicPlayerWidget::onSeekSliderReleased()
{
    isSeekSliderPressed = false;
    mediaPlayer->setPosition(seekSlider->value());
}

void MusicPlayerWidget::onSeekSliderMoved(int position)
{
    currentTimeLabel->setText(formatTime(position));
}

// 播放列表操作
void MusicPlayerWidget::onAddMusicClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(
        this,
        "选择音乐文件",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "音频文件 (*.mp3 *.wav *.flac *.aac *.ogg *.wma);;所有文件 (*.*)"
    );

    for (const QString &fileName : fileNames) {
        addMusicToPlaylist(fileName);
    }
}

void MusicPlayerWidget::onRemoveMusicClicked()
{
    int currentRow = playlistWidget->currentRow();
    if (currentRow >= 0) {
        // 从播放列表中移除
        playlist->removeMedia(currentRow);

        // 从界面列表中移除
        QListWidgetItem *item = playlistWidget->takeItem(currentRow);
        delete item;

        QMessageBox::information(this, "成功", "已删除选中的音乐文件！");
    } else {
        QMessageBox::warning(this, "警告", "请先选择要删除的音乐文件！");
    }
}

void MusicPlayerWidget::onPlaylistItemDoubleClicked(QListWidgetItem *item)
{
    int row = playlistWidget->row(item);
    playlist->setCurrentIndex(row);
    mediaPlayer->play();
}

// 定时器更新
void MusicPlayerWidget::updatePlayTime()
{
    // 这里可以添加额外的定时更新逻辑
    // 目前播放时间更新通过positionChanged信号处理
}

// 辅助方法
void MusicPlayerWidget::updatePlayModeButton()
{
    switch (currentPlayMode) {
    case Sequential:
        playModeButton->setText("顺序播放");
        break;
    case Loop:
        playModeButton->setText("列表循环");
        break;
    case CurrentItemInLoop:
        playModeButton->setText("单曲循环");
        break;
    case Random:
        playModeButton->setText("随机播放");
        break;
    }
}

void MusicPlayerWidget::updatePlayPauseButton()
{
    if (mediaPlayer->state() == QMediaPlayer::PlayingState) {
        playPauseButton->setText("暂停");
    } else {
        playPauseButton->setText("播放");
    }
}

void MusicPlayerWidget::addMusicToPlaylist(const QString &filePath)
{
    // 添加到媒体播放列表
    playlist->addMedia(QUrl::fromLocalFile(filePath));

    // 添加到界面列表
    QFileInfo fileInfo(filePath);
    QString fileName = fileInfo.baseName();

    QListWidgetItem *item = new QListWidgetItem(fileName);
    item->setData(Qt::UserRole, filePath); // 存储完整路径
    item->setToolTip(filePath); // 设置工具提示显示完整路径

    playlistWidget->addItem(item);
}

QString MusicPlayerWidget::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}
