#ifndef MUSICPLAYERWIDGET_H
#define MUSICPLAYERWIDGET_H

#include <QWidget>
#include <QMediaPlayer>
#include <QMediaPlaylist>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QListWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFileDialog>
#include <QTime>
#include <QTimer>
#include <QMessageBox>
#include <QApplication>
#include <QStyle>
#include <QScrollArea>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

QT_BEGIN_NAMESPACE
class QMediaPlayer;
class QMediaPlaylist;
class QLabel;
class QPushButton;
class QSlider;
class QListWidget;
QT_END_NAMESPACE

class PlaylistWidget;

class MusicPlayerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit MusicPlayerWidget(QWidget *parent = nullptr);
    ~MusicPlayerWidget();

private slots:
    // 播放控制
    void onPlayPauseClicked();
    void onPreviousClicked();
    void onNextClicked();
    void onStopClicked();
    
    // 音量控制
    void onVolumeChanged(int volume);
    void onVolumeUpClicked();
    void onVolumeDownClicked();
    
    // 播放模式
    void onPlayModeClicked();
    
    // 播放器状态
    void onStateChanged(QMediaPlayer::State state);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);
    
    // 进度条控制
    void onSeekSliderPressed();
    void onSeekSliderReleased();
    void onSeekSliderMoved(int position);
    
    // 歌单操作
    void onAddMusicClicked();
    void onRemoveMusicClicked();
    void onPlaylistItemDoubleClicked(QListWidgetItem *item);
    void onFavoriteClicked();
    void onThemeClicked();
    void onAddThemeImageClicked();

    // 定时器更新
    void updatePlayTime();

    // 主题相关
    void switchTheme();
    void loadThemeImages();
    void applyTheme(const QString &imagePath);

private:
    void setupUI();
    void setupConnections();
    void setupMediaPlayer();
    void updatePlayModeButton();
    void updatePlayPauseButton();
    void addMusicToPlaylist(const QString &filePath);
    QString formatTime(qint64 milliseconds);
    void saveFavorites();
    void loadFavorites();
    QString extractSongTitle(const QString &filePath);
    void updatePlaylistDisplay();
    
    // 媒体播放器
    QMediaPlayer *mediaPlayer;
    QMediaPlaylist *playlist;
    
    // 播放模式枚举
    enum PlayMode {
        Sequential,     // 顺序播放
        Loop,          // 列表循环
        CurrentItemInLoop, // 单曲循环
        Random         // 随机播放
    };
    PlayMode currentPlayMode;
    
    // UI组件 - 歌曲信息区域
    QLabel *songTitleLabel;
    QLabel *artistLabel;
    QLabel *albumLabel;
    
    // UI组件 - 播放控制区域
    QPushButton *previousButton;
    QPushButton *playPauseButton;
    QPushButton *nextButton;
    QPushButton *stopButton;
    QPushButton *playModeButton;
    
    // UI组件 - 进度控制
    QSlider *seekSlider;
    QLabel *currentTimeLabel;
    QLabel *totalTimeLabel;
    
    // UI组件 - 音量控制
    QSlider *volumeSlider;
    QLabel *volumeLabel;
    QPushButton *volumeUpButton;
    QPushButton *volumeDownButton;
    
    // UI组件 - 播放列表
    QScrollArea *playlistScrollArea;
    QListWidget *playlistWidget;
    QPushButton *addMusicButton;
    QPushButton *removeMusicButton;
    QPushButton *favoriteButton;
    QPushButton *themeButton;
    QPushButton *addThemeButton;
    
    // 布局
    QVBoxLayout *mainLayout;
    QHBoxLayout *infoLayout;
    QHBoxLayout *controlLayout;
    QHBoxLayout *seekLayout;
    QHBoxLayout *volumeLayout;
    QHBoxLayout *playlistControlLayout;
    
    // 定时器
    QTimer *updateTimer;
    
    // 状态变量
    bool isSeekSliderPressed;

    // 主题和收藏相关
    QStringList favoriteList;
    QStringList themeImages;
    int currentThemeIndex;
    QString currentThemePath;
};

#endif // MUSICPLAYERWIDGET_H
