#ifndef MUSICPLAYERWIDGET_H
#define MUSICPLAYERWIDGET_H

#include <QWidget>
#include <QMediaPlayer>
#include <QMediaPlaylist>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QListWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFileDialog>
#include <QTimer>
#include <QMessageBox>
#include <QPropertyAnimation>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QInputDialog>
#include <QMenu>
#include <QAction>
#include <QMap>
#include <QRegExp>

QT_BEGIN_NAMESPACE
class QMediaPlayer;
class QMediaPlaylist;
class QLabel;
class QPushButton;
class QSlider;
class QListWidget;
QT_END_NAMESPACE

class MusicPlayerWidget : public QWidget
{
    Q_OBJECT

public:
    explicit MusicPlayerWidget(QWidget *parent = nullptr);
    ~MusicPlayerWidget();

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    // 播放控制
    void onPlayPauseClicked();
    void onPreviousClicked();
    void onNextClicked();
    void onStopClicked();
    
    // 音量控制
    void onVolumeChanged(int volume);
    void onVolumeUpClicked();
    void onVolumeDownClicked();
    
    // 播放模式
    void onPlayModeClicked();
    
    // 播放器状态
    void onStateChanged(QMediaPlayer::State state);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);
    
    // 进度条控制
    void onSeekSliderPressed();
    void onSeekSliderReleased();
    void onSeekSliderMoved(int position);
    
    // 歌单操作
    void onAddMusicClicked();
    void onRemoveMusicClicked();
    void onPlaylistItemClicked(QListWidgetItem *item);
    void onPlaylistItemDoubleClicked(QListWidgetItem *item);
    void onPlaylistItemRightClicked(const QPoint &pos);
    void onRenameSongClicked();
    void onFavoriteClicked();
    void onAlbumCoverClicked();

    // 背景图片相关
    void switchBackgroundImage();
    void loadBackgroundImages();
    void applyBackgroundImage(const QString &imagePath);
    void setDefaultBackground();

private:
    void setupUI();
    void setupConnections();
    void setupMediaPlayer();
    void updatePlayModeButton();
    void updatePlayPauseButton();
    void addMusicToPlaylist(const QString &filePath);
    QString formatTime(qint64 milliseconds);
    void saveFavorites();
    void loadFavorites();
    QString extractSongTitle(const QString &filePath);
    void updatePlaylistDisplay();
    void updatePlaylistHighlight();
    void updatePlayingItemStyle();
    void saveAlbumCovers();
    void loadAlbumCovers();
    void updateAlbumCover(const QString &filePath);
    void saveCustomSongNames();
    void loadCustomSongNames();
    QString getDisplayName(const QString &filePath);
    QString getNetEaseCloudMusicStyle();
    void saveBackgroundImages();
    
    // 媒体播放器
    QMediaPlayer *mediaPlayer;
    QMediaPlaylist *playlist;
    
    // 播放模式枚举
    enum PlayMode {
        Sequential,     // 顺序播放
        Loop,          // 列表循环
        CurrentItemInLoop, // 单曲循环
        Random         // 随机播放
    };
    PlayMode currentPlayMode;
    
    // UI组件 - 歌曲信息区域
    QLabel *songTitleLabel;
    QLabel *artistLabel;
    QLabel *albumLabel;
    QLabel *albumCoverLabel;
    
    // UI组件 - 播放控制区域
    QPushButton *previousButton;
    QPushButton *playPauseButton;
    QPushButton *nextButton;
    QPushButton *stopButton;
    QPushButton *playModeButton;
    
    // UI组件 - 进度控制
    QSlider *seekSlider;
    QLabel *currentTimeLabel;
    QLabel *totalTimeLabel;
    
    // UI组件 - 音量控制
    QSlider *volumeSlider;
    QLabel *volumeLabel;
    QPushButton *volumeUpButton;
    QPushButton *volumeDownButton;
    
    // UI组件 - 播放列表
    QListWidget *playlistWidget;
    QPushButton *addMusicButton;
    QPushButton *removeMusicButton;
    QPushButton *favoriteButton;

    
    // 布局
    QVBoxLayout *mainLayout;
    QHBoxLayout *infoLayout;
    QHBoxLayout *controlLayout;
    QHBoxLayout *seekLayout;
    QHBoxLayout *volumeLayout;
    QHBoxLayout *playlistControlLayout;
    
    // 状态变量
    bool isSeekSliderPressed;

    // 收藏相关
    QStringList favoriteList;

    // 自定义歌曲名
    QMap<QString, QString> customSongNames; // 文件路径 -> 自定义名称

    // 专辑封面相关
    QMap<QString, QString> albumCovers; // 文件路径 -> 封面图片路径

    // 背景图片相关
    QStringList backgroundImages;
    int currentBackgroundIndex;
    QString currentBackgroundPath;
};

#endif // MUSICPLAYERWIDGET_H
