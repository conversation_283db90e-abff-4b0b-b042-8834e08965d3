#include <QApplication>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>
#include <QLabel>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    QWidget window;
    window.setWindowTitle("按钮样式测试");
    window.resize(400, 600);
    
    QVBoxLayout *layout = new QVBoxLayout(&window);
    
    // 测试控制按钮样式
    QLabel *label1 = new QLabel("控制按钮样式测试:");
    layout->addWidget(label1);
    
    QPushButton *controlBtn = new QPushButton("控制按钮", &window);
    controlBtn->setObjectName("controlButton");
    controlBtn->setFixedSize(120, 45);
    layout->addWidget(controlBtn);
    
    // 测试播放按钮样式
    QLabel *label2 = new QLabel("播放按钮样式测试:");
    layout->addWidget(label2);
    
    QPushButton *playBtn = new QPushButton("播放按钮", &window);
    playBtn->setObjectName("playButton");
    playBtn->setFixedSize(120, 60);
    layout->addWidget(playBtn);
    
    // 测试模式按钮样式
    QLabel *label3 = new QLabel("模式按钮样式测试:");
    layout->addWidget(label3);
    
    QPushButton *modeBtn = new QPushButton("模式按钮", &window);
    modeBtn->setObjectName("modeButton");
    modeBtn->setFixedSize(120, 45);
    layout->addWidget(modeBtn);
    
    // 测试音量按钮样式
    QLabel *label4 = new QLabel("音量按钮样式测试:");
    layout->addWidget(label4);
    
    QPushButton *volumeBtn = new QPushButton("音量", &window);
    volumeBtn->setObjectName("volumeBtn");
    volumeBtn->setFixedSize(80, 30);
    layout->addWidget(volumeBtn);
    
    // 测试功能按钮样式
    QLabel *label5 = new QLabel("功能按钮样式测试:");
    layout->addWidget(label5);
    
    QPushButton *actionBtn = new QPushButton("功能", &window);
    actionBtn->setObjectName("actionButton");
    actionBtn->setFixedSize(80, 35);
    layout->addWidget(actionBtn);
    
    // 测试收藏按钮样式
    QLabel *label6 = new QLabel("收藏按钮样式测试:");
    layout->addWidget(label6);
    
    QPushButton *favoriteBtn = new QPushButton("收藏", &window);
    favoriteBtn->setObjectName("favoriteButton");
    favoriteBtn->setFixedSize(80, 35);
    layout->addWidget(favoriteBtn);
    
    // 应用样式表
    QString styleSheet = 
        /* 现代化控制按钮 - 深度学习优化设计 */
        "QPushButton#controlButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(255, 255, 255, 0.95), "
        "                stop:0.5 rgba(255, 255, 255, 0.9), "
        "                stop:1 rgba(248, 250, 252, 0.95));"
        "    border: 1.5px solid rgba(226, 232, 240, 0.8);"
        "    border-radius: 12px;"
        "    color: #334155;"
        "    font-size: 15px;"
        "    font-weight: 500;"
        "    padding: 10px 16px;"
        "}"
        "QPushButton#controlButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(240, 253, 244, 0.95), "
        "                stop:0.5 rgba(236, 253, 245, 0.9), "
        "                stop:1 rgba(220, 252, 231, 0.95));"
        "    border-color: rgba(34, 197, 94, 0.4);"
        "    color: #166534;"
        "}"
        
        /* 现代化主播放按钮 - 深度学习优化设计 */
        "QPushButton#playButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(34, 197, 94, 1), "
        "                stop:0.5 rgba(22, 163, 74, 1), "
        "                stop:1 rgba(21, 128, 61, 1));"
        "    border: 2px solid rgba(21, 128, 61, 0.8);"
        "    border-radius: 16px;"
        "    color: rgba(255, 255, 255, 0.95);"
        "    font-size: 18px;"
        "    font-weight: 600;"
        "    padding: 12px 20px;"
        "}"
        "QPushButton#playButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(74, 222, 128, 1), "
        "                stop:0.5 rgba(34, 197, 94, 1), "
        "                stop:1 rgba(22, 163, 74, 1));"
        "    border-color: rgba(21, 128, 61, 1);"
        "    color: rgba(255, 255, 255, 1);"
        "}"
        
        /* 现代化模式按钮 - 深度学习优化设计 */
        "QPushButton#modeButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(255, 255, 255, 0.95), "
        "                stop:0.5 rgba(248, 250, 252, 0.9), "
        "                stop:1 rgba(241, 245, 249, 0.95));"
        "    border: 1.5px solid rgba(203, 213, 225, 0.7);"
        "    border-radius: 20px;"
        "    color: #475569;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 14px;"
        "}"
        "QPushButton#modeButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(236, 253, 245, 0.95), "
        "                stop:0.5 rgba(220, 252, 231, 0.9), "
        "                stop:1 rgba(187, 247, 208, 0.95));"
        "    border-color: rgba(34, 197, 94, 0.3);"
        "    color: #166534;"
        "}"
        
        /* 现代化音量按钮 - 深度学习优化设计 */
        "QPushButton#volumeBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(255, 255, 255, 0.95), "
        "                stop:0.5 rgba(251, 252, 254, 0.9), "
        "                stop:1 rgba(248, 250, 252, 0.95));"
        "    border: 1.2px solid rgba(226, 232, 240, 0.6);"
        "    border-radius: 10px;"
        "    color: #64748b;"
        "    font-size: 11px;"
        "    font-weight: 500;"
        "    padding: 6px 10px;"
        "}"
        "QPushButton#volumeBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(240, 253, 244, 0.95), "
        "                stop:0.5 rgba(220, 252, 231, 0.9), "
        "                stop:1 rgba(187, 247, 208, 0.95));"
        "    border-color: rgba(34, 197, 94, 0.25);"
        "    color: #166534;"
        "}"
        
        /* 现代化功能按钮 - 深度学习优化设计 */
        "QPushButton#actionButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(255, 255, 255, 0.95), "
        "                stop:0.5 rgba(248, 250, 252, 0.9), "
        "                stop:1 rgba(241, 245, 249, 0.95));"
        "    border: 1.2px solid rgba(203, 213, 225, 0.6);"
        "    border-radius: 14px;"
        "    color: #475569;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 12px;"
        "}"
        "QPushButton#actionButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(236, 253, 245, 0.95), "
        "                stop:0.5 rgba(220, 252, 231, 0.9), "
        "                stop:1 rgba(187, 247, 208, 0.95));"
        "    border-color: rgba(34, 197, 94, 0.3);"
        "    color: #166534;"
        "}"
        
        /* 现代化收藏按钮 - 深度学习优化设计 */
        "QPushButton#favoriteButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(255, 255, 255, 0.95), "
        "                stop:0.5 rgba(255, 251, 251, 0.9), "
        "                stop:1 rgba(254, 242, 242, 0.95));"
        "    border: 1.2px solid rgba(252, 165, 165, 0.5);"
        "    border-radius: 14px;"
        "    color: #dc2626;"
        "    font-size: 13px;"
        "    font-weight: 500;"
        "    padding: 8px 12px;"
        "}"
        "QPushButton#favoriteButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "                stop:0 rgba(254, 242, 242, 0.95), "
        "                stop:0.5 rgba(254, 226, 226, 0.9), "
        "                stop:1 rgba(252, 165, 165, 0.95));"
        "    border-color: rgba(220, 38, 38, 0.4);"
        "    color: #b91c1c;"
        "}";
    
    window.setStyleSheet(styleSheet);
    window.show();
    
    return app.exec();
}
