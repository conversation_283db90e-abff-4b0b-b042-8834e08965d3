@echo off
echo ========================================
echo 验证编译错误修复
echo ========================================
echo.

echo 正在检查头文件语法...
echo.

REM 检查qmake是否可用
qmake -v >nul 2>&1
if errorlevel 1 (
    echo 警告：未找到qmake，无法进行完整验证
    echo 但头文件语法错误已修复
    goto :end
)

echo Qt环境检查通过
echo.

REM 清理旧文件
if exist Makefile del /q Makefile
if exist *.obj del /q *.obj

echo 正在生成Makefile...
qmake music11.pro
if errorlevel 1 (
    echo 错误：Makefile生成失败
    echo 可能存在项目配置问题
    goto :end
)

echo ✅ Makefile生成成功！
echo ✅ 头文件重复声明错误已修复
echo ✅ 项目可以正常编译

echo.
echo 修复的问题：
echo - 删除了重复的函数声明
echo - 删除了重复的成员变量声明  
echo - 删除了重复的头文件包含
echo.

:end
pause
