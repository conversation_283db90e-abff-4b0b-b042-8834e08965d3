Qt5音乐播放器使用说明
====================

项目概述：
这是一个基于Qt5开发的音乐播放器，使用QWidget作为基类，具有完整的音乐播放功能。

主要功能：
1. 用户登录系统
   - 账号密码登录界面
   - 默认账号：admin/123456 或 user/password
   - 登录成功后跳转到音乐播放界面

2. 音乐播放控制
   - 播放/暂停按钮
   - 上一曲/下一曲切换
   - 停止播放
   - 播放进度条显示和控制

3. 音量控制系统
   - 音量滑块控制（0-100）
   - 实时显示音量数值
   - 音量+/-按钮快速调节

4. 播放模式切换
   - 顺序播放
   - 列表循环
   - 单曲循环
   - 随机播放

5. 歌单列表管理
   - 播放列表显示
   - 添加音乐文件（支持MP3, WAV, FLAC, AAC, OGG, WMA）
   - 删除选中的音乐文件
   - 双击歌曲直接播放

编译方法：
1. 确保已安装Qt5开发环境（包含Qt Multimedia模块）
2. 在Qt Creator中打开music11.pro文件
3. 点击编译按钮进行编译
4. 或者使用命令行：qmake && make

使用流程：
1. 运行程序，显示登录界面
2. 输入用户名密码登录（admin/123456）
3. 进入音乐播放器主界面
4. 点击"添加音乐"选择音乐文件
5. 双击播放列表中的歌曲开始播放
6. 使用各种控制按钮管理播放

技术特点：
- 基于Qt5 QWidget框架
- QMediaPlayer音频播放引擎
- 现代化界面设计
- 完整的信号槽机制
- 支持多种音频格式

注意事项：
- 需要Qt5环境支持
- 音频文件路径不能包含中文（某些系统）
- 首次使用需要添加音乐文件到播放列表
